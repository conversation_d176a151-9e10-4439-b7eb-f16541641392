@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-plus-circle me-3"></i>Add Previous Winner
                            </h2>
                            <p class="text-white mb-0">Create a new winner record with video content</p>
                        </div>
                        <a href="{{ route('admin.previous-winners.index') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-crown me-2"></i>Winner Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.previous-winners.store') }}" method="POST">
                        @csrf
                        {{-- Event ID will be automatically set by the controller using EventFilterTrait --}}

                        <div class="mb-4">
                            <label for="event_name" class="form-label fw-semibold">
                                <i class="fas fa-calendar-alt me-2 text-primary"></i>Event Name *
                            </label>
                            <input type="text" class="form-control @error('event_name') is-invalid @enderror"
                                id="event_name" name="event_name" value="{{ old('event_name') }}"
                                placeholder="Enter event name" required>
                            @error('event_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="winner_name" class="form-label fw-semibold">
                                    <i class="fas fa-user me-2 text-success"></i>Winner Name
                                </label>
                                <input type="text" class="form-control @error('winner_name') is-invalid @enderror"
                                    id="winner_name" name="winner_name" value="{{ old('winner_name') }}"
                                    placeholder="Enter winner's name">
                                @error('winner_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>


                        </div>

                        <div class="mb-4">
                            <label for="video_url" class="form-label fw-semibold">
                                <i class="fas fa-video me-2 text-danger"></i>Video URL
                            </label>
                            <input type="url" class="form-control @error('video_url') is-invalid @enderror"
                                id="video_url" name="video_url" value="{{ old('video_url') }}"
                                placeholder="Enter video URL (YouTube, Vimeo, etc.)">
                            @error('video_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Optional: URL to the winner's video content</div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label fw-semibold">
                                <i class="fas fa-align-left me-2 text-secondary"></i>Description
                            </label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description"
                                name="description" rows="4"
                                placeholder="Enter event description or winner details">{{ old('description') }}</textarea>
                            @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                    value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label fw-semibold" for="is_active">
                                    <i class="fas fa-toggle-on me-2 text-success"></i>Active Status
                                </label>
                                <div class="form-text">Enable this winner record to be visible</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.previous-winners.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Winner Record
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on first input
    document.getElementById('event_name').focus();
    
    // Video URL validation
    const videoUrlInput = document.getElementById('video_url');
    videoUrlInput.addEventListener('blur', function() {
        if (this.value) {
            // Basic validation for common video platforms
            const videoPattern = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be|vimeo\.com|dailymotion\.com)/i;
            if (!videoPattern.test(this.value)) {
                this.classList.add('is-invalid');
                // You can add custom validation feedback here
            } else {
                this.classList.remove('is-invalid');
            }
        }
    });
});
</script>
@endsection