@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-edit me-3"></i>Edit Special Reward
                            </h2>
                            <p class="text-white mb-0">Update reward information and validity period</p>
                        </div>
                        <div>
                            <a href="{{ route('admin.special-rewards.show', $specialReward->id) }}"
                                class="btn btn-light me-2">
                                <i class="fas fa-eye me-2"></i>View
                            </a>
                            <a href="{{ route('admin.special-rewards.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-gift me-2"></i>Reward Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.special-rewards.update', $specialReward->id) }}" method="POST"
                        enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="mb-4">
                            <label for="reward_name" class="form-label fw-semibold">
                                <i class="fas fa-gift me-2 text-primary"></i>Reward Name *
                            </label>
                            <input type="text" class="form-control @error('reward_name') is-invalid @enderror"
                                id="reward_name" name="reward_name"
                                value="{{ old('reward_name', $specialReward->reward_name) }}"
                                placeholder="Enter reward name" required>
                            @error('reward_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="data_plan" class="form-label fw-semibold">
                                    <i class="fas fa-wifi me-2 text-info"></i>Data Plan *
                                </label>
                                <input type="text" class="form-control @error('data_plan') is-invalid @enderror"
                                    id="data_plan" name="data_plan"
                                    value="{{ old('data_plan', $specialReward->data_plan) }}"
                                    placeholder="e.g., 5GB, Unlimited, etc." required>
                                @error('data_plan')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="validity_period_days" class="form-label fw-semibold">
                                    <i class="fas fa-calendar-days me-2 text-warning"></i>Validity Period (Days) *
                                </label>
                                <input type="number"
                                    class="form-control @error('validity_period_days') is-invalid @enderror"
                                    id="validity_period_days" name="validity_period_days"
                                    value="{{ old('validity_period_days', $specialReward->validity_period_days) }}"
                                    min="1" placeholder="30" required>
                                @error('validity_period_days')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Number of days the reward is valid</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="price" class="form-label fw-semibold">
                                    <i class="fas fa-dollar-sign me-2 text-success"></i>Price
                                </label>
                                <input type="number" class="form-control @error('price') is-invalid @enderror"
                                    id="price" name="price" value="{{ old('price', $specialReward->price) }}"
                                    step="0.01" min="0" placeholder="0.00">
                                @error('price')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Leave empty for free rewards</div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="image" class="form-label fw-semibold">
                                    <i class="fas fa-image me-2 text-secondary"></i>Upload Image
                                </label>
                                <input type="file" class="form-control @error('image') is-invalid @enderror" id="image"
                                    name="image" accept="image/*">
                                @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Optional: Upload the reward image (JPEG, PNG, GIF, WebP - Max
                                    5MB)</div>
                                @if($specialReward->image_url)
                                <div class="mt-2">
                                    <small class="text-muted">Current image:</small><br>
                                    <img src="{{ $specialReward->image_url }}" alt="Current image" class="img-thumbnail"
                                        style="max-width: 200px; max-height: 200px;">
                                </div>
                                @endif
                                <div id="image-preview" class="mt-2" style="display: none;">
                                    <small class="text-muted">New image preview:</small><br>
                                    <img id="preview-image" src="" alt="Preview" class="img-thumbnail"
                                        style="max-width: 200px; max-height: 200px;">
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label fw-semibold">
                                <i class="fas fa-align-left me-2 text-secondary"></i>Description
                            </label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description"
                                name="description" rows="4"
                                placeholder="Enter reward description and details">{{ old('description', $specialReward->description) }}</textarea>
                            @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="product_url" class="form-label fw-semibold">
                                <i class="fas fa-link me-2 text-info"></i>Product URL
                            </label>
                            <input type="url" class="form-control @error('product_url') is-invalid @enderror"
                                id="product_url" name="product_url"
                                value="{{ old('product_url', $specialReward->product_url) }}"
                                placeholder="https://example.com/product">
                            @error('product_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Optional: External link to the product page</div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                    value="1" {{ old('is_active', $specialReward->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label fw-semibold" for="is_active">
                                    <i class="fas fa-toggle-on me-2 text-success"></i>Active Status
                                </label>
                                <div class="form-text">Enable this reward to be available</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.special-rewards.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Reward
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on first input
    document.getElementById('reward_name').focus();

    // Price formatting
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('blur', function() {
        if (this.value) {
            this.value = parseFloat(this.value).toFixed(2);
        }
    });

    // Image upload preview
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('image-preview');
    const previewImage = document.getElementById('preview-image');

    imageInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB');
                this.value = '';
                imagePreview.style.display = 'none';
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid image file (JPEG, PNG, GIF, WebP)');
                this.value = '';
                imagePreview.style.display = 'none';
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
        }
    });
});
</script>
@endsection