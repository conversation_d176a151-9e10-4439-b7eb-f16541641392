<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SpecialReward extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'event_id',
        'image',
        'reward_name',
        'data_plan',
        'validity_period_days',
        'price',
        'description',
        'product_url',
        'is_active'
    ];

    protected $casts = [
        'validity_period_days' => 'integer',
        'price' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        if ($this->image && !str_starts_with($this->image, 'http')) {
            return asset('storage/' . $this->image);
        }
        return $this->image;
    }

    public function getFullImageUrlAttribute()
    {
        return $this->image_url;
    }

    // Helper methods for validity calculation
    public function getExpirationDateAttribute()
    {
        return $this->created_at->addDays($this->validity_period_days);
    }

    public function getIsExpiredAttribute()
    {
        return now()->isAfter($this->expiration_date);
    }

    public function getDaysRemainingAttribute()
    {
        $remaining = now()->diffInDays($this->expiration_date, false);
        return max(0, $remaining);
    }

    public function getValidityStatusAttribute()
    {
        if ($this->is_expired) {
            return 'expired';
        } elseif ($this->days_remaining <= 7) {
            return 'expiring_soon';
        } else {
            return 'active';
        }
    }

    public function getFormattedValidityAttribute()
    {
        if ($this->is_expired) {
            return 'Expired';
        } elseif ($this->days_remaining == 0) {
            return 'Expires today';
        } elseif ($this->days_remaining == 1) {
            return 'Expires tomorrow';
        } else {
            return "Expires in {$this->days_remaining} days";
        }
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeNotExpired($query)
    {
        return $query->whereRaw('DATE_ADD(created_at, INTERVAL validity_period_days DAY) > NOW()');
    }

    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->whereRaw('DATE_ADD(created_at, INTERVAL validity_period_days DAY) BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY)', [$days]);
    }
}
