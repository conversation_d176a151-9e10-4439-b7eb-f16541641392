<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\EventController;
use App\Http\Controllers\Api\EventScheduleController;
use App\Http\Controllers\Api\FaqController;
use App\Http\Controllers\Api\FinalistController;
use App\Http\Controllers\Api\PreviousWinnerController;
use App\Http\Controllers\Api\MerchandisePackageController;
use App\Http\Controllers\Api\SpecialRewardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Korea Kaja CMS API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('v1')->group(function () {
    // Events API
    Route::apiResource('events', EventController::class);

    // Event Schedules API
    Route::apiResource('event-schedules', EventScheduleController::class);

    // FAQs API
    Route::get('faqs/sections', [FaqController::class, 'sections']);
    Route::apiResource('faqs', FaqController::class);

    // Finalists API
    Route::apiResource('finalists', FinalistController::class);

    // Previous Winners API
    Route::apiResource('previous-winners', PreviousWinnerController::class);

    // Merchandise Packages API
    Route::apiResource('merchandise-packages', MerchandisePackageController::class);

    // Special Rewards API
    Route::apiResource('special-rewards', SpecialRewardController::class);

    // Special Quotas API
    Route::apiResource('special-quotas', \App\Http\Controllers\Api\SpecialQuotaController::class);
});
