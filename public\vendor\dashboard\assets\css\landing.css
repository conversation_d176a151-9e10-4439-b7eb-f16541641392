.client-block img,
.drp-tech-scrollble a[href="#"],
.nav-drp-tech-scrollble a[href="#"] {
    filter: grayscale(1);
}
body {
    font-feature-settings: "salt";
}
h1,
h2 {
    font-weight: 700;
}
:root {
    --bs-body-bg: #f8f9fa;
    --bs-body-bg-rgb: 248, 249, 250;
    --pc-heading-color: #1d2630;
    --pc-active-background: #f3f5f7;
    --pc-sidebar-background: transparent;
    --pc-sidebar-color: #5b6b79;
    --pc-sidebar-color-rgb: 91, 107, 121;
    --pc-sidebar-active-color: #1c582c;
    --pc-sidebar-shadow: none;
    --pc-sidebar-caption-color: #3e4853;
    --pc-sidebar-border: 1px dashed #bec8d0;
    --pc-sidebar-user-background: #f3f5f7;
    --pc-header-background: rgba(var(--bs-body-bg-rgb), 0.7);
    --pc-header-color: #5b6b79;
    --pc-header-shadow: none;
    --pc-card-box-shadow: none;
    --pc-header-submenu-background: #ffffff;
    --pc-header-submenu-color: #5b6b79;
}
[data-pc-theme_contrast="true"] {
    --bs-body-bg: #ffffff;
    --pc-sidebar-background: transparent;
    --pc-sidebar-active-color: #1c582c;
    --pc-sidebar-shadow: 1px 0 3px 0px #dbe0e5;
    --pc-sidebar-border: none;
    --pc-card-box-shadow: 0px 8px 24px rgba(27, 46, 94, 0.08);
}
section {
    padding: 100px 0;
}
.title {
    margin-bottom: 50px;
}
.title h2 {
    font-weight: 600;
}
.navbar .nav-link,
.title h5 {
    font-weight: 500;
}
.landing-page {
    overflow-x: hidden;
    background: var(--bs-body-bg);
}
@media (min-width: 1600px) {
    .landing-page .container {
        max-width: 1200px;
    }
}
.landing-page .loader-bg {
    z-index: 1102;
}
.landing-page .tooltip {
    --bs-tooltip-zindex: 1102;
}
.navbar {
    position: fixed;
    padding: 16px 0;
    width: 100%;
    z-index: 1099;
    top: 0;
    -webkit-backdrop-filter: blur(7px);
    backdrop-filter: blur(7px);
    background-color: var(--pc-header-background);
}
.navbar.top-nav-collapse.default {
    box-shadow: none;
}
.navbar.default,
.navbar.top-nav-collapse {
    box-shadow: 0 8px 6px -10px rgba(0, 0, 0, 0.5);
}
.navbar .nav-link:active,
.navbar .nav-link:focus,
.navbar .nav-link:hover {
    color: var(--bs-primary);
}
header {
    overflow: hidden;
    position: relative;
    padding: 100px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-size: cover;
    flex-direction: column;
}
header .technology-block {
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    align-items: center;
    justify-content: center;
    background: var(--bs-white);
    border-top: var(--bs-border-width) var(--bs-border-style)
        var(--bs-border-color);
    border-bottom: var(--bs-border-width) var(--bs-border-style)
        var(--bs-border-color);
}
header .technology-block .list-inline {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
}
header .technology-block.simplebar-scrollable-x .list-inline {
    justify-content: flex-start;
}
header .technology-block .list-inline-item {
    flex-shrink: 0;
    border-right: var(--bs-border-width) var(--bs-border-style)
        var(--bs-border-color);
    margin-right: 0;
}
header .technology-block .list-inline-item img {
    padding: 10px;
}
header .technology-block .list-inline-item:first-child {
    border-left: var(--bs-border-width) var(--bs-border-style)
        var(--bs-border-color);
}
header h1 {
    font-size: 55px;
}
header .head-rating-block {
    position: relative;
}
header .head-rating-block:after {
    content: "";
    position: absolute;
    top: 20%;
    bottom: 20%;
    left: auto;
    right: 0;
    width: 1px;
    background: var(--bs-border-color);
}
header .container {
    position: relative;
    z-index: 5;
}
.nav-drp-tech-scrollble {
    max-height: calc(100vh - 75px);
}
.drp-tech-scrollble {
    max-height: 40vh;
}
.drp-tech-scrollble,
.nav-drp-tech-scrollble {
    overflow-y: auto;
}
.drp-tech-scrollble::-webkit-scrollbar,
.nav-drp-tech-scrollble::-webkit-scrollbar {
    width: 6px;
    opacity: 0;
}
.drp-tech-scrollble::-webkit-scrollbar:hover,
.nav-drp-tech-scrollble::-webkit-scrollbar:hover {
    opacity: 1;
}
.drp-tech-scrollble::-webkit-scrollbar-track,
.nav-drp-tech-scrollble::-webkit-scrollbar-track {
    background: 0 0;
}
.drp-tech-scrollble::-webkit-scrollbar-thumb,
.nav-drp-tech-scrollble::-webkit-scrollbar-thumb {
    background: #e9ecef;
}
.drp-tech-scrollble::-webkit-scrollbar-thumb:hover,
.nav-drp-tech-scrollble::-webkit-scrollbar-thumb:hover {
    background: #aeb9c5;
}
.hero-text-gradient {
    --bg-size: 400%;
    --color-one: rgb(37, 161, 244);
    --color-two: rgb(249, 31, 169);
    background: linear-gradient(
            90deg,
            var(--color-one),
            var(--color-two),
            var(--color-one)
        )
        0 0 / var(--bg-size) 100%;
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    animation: move-bg 24s infinite linear;
}
@keyframes move-bg {
    to {
        background-position: var(--bg-size) 0;
    }
}
.app-link {
    padding: 30px;
    margin: 10px 0;
    display: block;
    border-radius: 12px;
    cursor: pointer;
    transition: all 80ms cubic-bezier(0.37, 0.24, 0.53, 0.99);
}
.app-link.active,
.app-link:hover {
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 8px 24px rgba(27, 46, 94, 0.08);
}
.img-app-moke {
    transform-origin: 0 0;
    transform: scale(1.5);
}
.workspace-card-block .card {
    transition: all 80ms cubic-bezier(0.37, 0.24, 0.53, 0.99);
    position: relative;
}
.workspace-card-block .card .avtar {
    background: rgba(204, 204, 204, 0.25);
    transition: all 80ms cubic-bezier(0.37, 0.24, 0.53, 0.99);
    margin-bottom: 24px;
}
.workspace-card-block .card .avtar svg {
    width: 30px;
    height: 30px;
    color: var(--bs-gray-500);
    transition: all 80ms cubic-bezier(0.37, 0.24, 0.53, 0.99);
}
.footer .footer-link a,
.workspace-card-block .active .card .avtar svg,
.workspace-card-block .card:hover .avtar svg {
    color: var(--bs-primary);
}
.workspace-card-block .active .card,
.workspace-card-block .card:hover {
    background: rgba(204, 204, 204, 0.1);
}
.workspace-card-block .active .card .avtar,
.workspace-card-block .card:hover .avtar {
    background: rgba(var(--bs-primary-rgb), 0.25);
}
.workspace-card-block .active .card::after {
    content: "";
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: var(--bs-primary);
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translate(-50%, -50%);
}
.workspace-slider-block {
    overflow: hidden;
    background-size: cover;
    padding: 35px 0;
}
.workspace-slider-block .img-ws-slider {
    margin: 30px 0;
    box-shadow: 0 8px 24px rgba(27, 46, 94, 0.12);
}
.workspace-slider-block .owl-item:not(.active) .img-ws-slider {
    opacity: 0.5;
}
.support-team-block {
    overflow: hidden;
    background-size: cover;
}
.support-team-block .support-card {
    width: 420px;
    margin-bottom: 0;
}
.support-team-block .support-card p {
    white-space: pre-wrap;
    height: 62px;
    overflow: hidden;
}
.marquee-list {
    display: flex;
    flex-wrap: nowrap;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    margin-bottom: 20px;
}
.marquee-list li {
    margin: 0 10px;
    cursor: pointer;
}
.marquee-list li:hover {
    opacity: 1;
}
.marquee-text {
    position: relative;
    height: 150px;
    display: inline-flex;
}
.marquee-text .js-marquee {
    margin-right: 0 !important;
}
.img-suggest-moke {
    max-width: 1110px;
    float: right;
}
.client-block img {
    transition: all 80ms cubic-bezier(0.37, 0.24, 0.53, 0.99);
    opacity: 0.4;
    cursor: pointer;
}
.client-block img:hover {
    filter: grayscale(0);
    opacity: 1;
}
.footer {
    padding: 100px 0 20px;
    background-size: cover;
}
.footer .footer-center {
    padding: 60px 0;
    margin: 60px 0 20px;
}
.footer .footer-link a {
    margin: 14px 0;
    display: block;
    transition: all 80ms cubic-bezier(0.37, 0.24, 0.53, 0.99);
}
.footer .footer-link a:not(:hover) {
    color: var(--bs-body-color);
    opacity: 0.9;
}
.footer .footer-sos-link a {
    transition: all 80ms cubic-bezier(0.37, 0.24, 0.53, 0.99);
    color: var(--bs-primary);
}
.footer .footer-sos-link a:not(:hover) {
    color: var(--bs-body-color);
    opacity: 0.9;
}
@media (max-width: 991.98px) {
    section {
        padding: 40px 0;
    }
    header .technology-block .list-inline-item img {
        padding: 10px;
        width: 60px;
    }
}
@media (max-width: 767.98px) {
    .img-suggest-moke {
        max-width: 100%;
        float: none;
        margin-bottom: 30px;
    }
    .workspace-card-block .card .card-body {
        display: flex;
        align-items: flex-start;
    }
    .workspace-card-block .card .card-body .avtar {
        margin-right: 24px;
        flex-shrink: 0;
    }
    header {
        text-align: center;
        padding: 100px 0;
    }
    header h1 {
        font-size: 25px;
    }
    header .technology-block .list-inline-item:first-child {
        border-left: none;
    }
    header .technology-block .list-inline-item:last-child {
        border-right: none;
    }
    header .technology-block .list-inline-item img {
        padding: 8px;
        width: 40px;
    }
    .footer .footer-center {
        padding: 30px 0;
        margin: 30px 0 20px;
    }
}
[data-pc-theme="dark"].landing-page .bg-white {
    background: #1b232d !important;
    --bs-white: lighten($dark-layout-color, 4%);
}
[data-pc-theme="dark"].landing-page .bg-gray-100 {
    background: var(--bs-dark);
}
[data-pc-theme="dark"].landing-page header {
    background: 0 0 !important;
}
[data-pc-theme="dark"].landing-page header .technology-block {
    background: #131920;
}
[data-pc-theme="dark"].landing-page .contact-hero {
    background-image: url(../images/landing/img-headerbg-dark.jpg) !important;
}
[data-pc-direction="rtl"] header .technology-block .list-inline-item {
    border-right: none;
    border-left: var(--bs-border-width) var(--bs-border-style)
        var(--bs-border-color);
    margin-left: 0;
}
[data-pc-direction="rtl"]
    header
    .technology-block
    .list-inline-item:first-child {
    border-right: var(--bs-border-width) var(--bs-border-style)
        var(--bs-border-color);
}
[data-pc-direction="rtl"] .img-app-moke {
    transform-origin: 100% 0;
    direction: ltr;
}
[data-pc-direction="rtl"] .marquee,
[data-pc-direction="rtl"] .marquee-1 {
    direction: ltr;
    float: left;
}
[data-pc-direction="rtl"] .marquee .d-flex,
[data-pc-direction="rtl"] .marquee-1 .d-flex {
    flex-direction: row-reverse;
}
