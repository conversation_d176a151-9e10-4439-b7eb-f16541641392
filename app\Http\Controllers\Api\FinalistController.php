<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Finalist;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class FinalistController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Finalist::query();

            // Filter by city if provided
            if ($request->has('city')) {
                $query->where('city', 'like', '%' . $request->city . '%');
            }

            // Filter by badge status
            if ($request->has('badge_status')) {
                $query->where('badge_status', $request->boolean('badge_status'));
            }

            // Filter by active status
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Search in name and username
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%')
                      ->orWhere('username', 'like', '%' . $search . '%');
                });
            }

            // Sort by rank
            $query->orderBy('rank', 'asc');

            $perPage = $request->get('per_page', 15);
            $finalists = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $finalists,
                'message' => 'Finalists retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve finalists',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'rank' => 'required|integer|min:1',
                'name' => 'required|string|max:255',
                'username' => 'required|string|max:255|unique:finalists,username',
                'photo' => 'nullable|string',
                'badge_status' => 'boolean',
                'city' => 'required|string|max:255',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $finalist = Finalist::create($validated);

            return response()->json([
                'success' => true,
                'data' => $finalist,
                'message' => 'Finalist created successfully'
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create finalist',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $finalist = Finalist::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $finalist,
                'message' => 'Finalist retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Finalist not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $finalist = Finalist::findOrFail($id);

            $validated = $request->validate([
                'rank' => 'sometimes|required|integer|min:1',
                'name' => 'sometimes|required|string|max:255',
                'username' => 'sometimes|required|string|max:255|unique:finalists,username,' . $id,
                'photo' => 'nullable|string',
                'badge_status' => 'boolean',
                'city' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $finalist->update($validated);

            return response()->json([
                'success' => true,
                'data' => $finalist->fresh(),
                'message' => 'Finalist updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update finalist',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $finalist = Finalist::findOrFail($id);
            $finalist->delete();

            return response()->json([
                'success' => true,
                'message' => 'Finalist deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete finalist',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
