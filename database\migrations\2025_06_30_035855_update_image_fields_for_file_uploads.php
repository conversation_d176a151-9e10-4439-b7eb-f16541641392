<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update finalists table: photo_url -> photo
        Schema::table('finalists', function (Blueprint $table) {
            $table->renameColumn('photo_url', 'photo');
        });

        // Update merchandise_packages table: image_url -> image
        Schema::table('merchandise_packages', function (Blueprint $table) {
            $table->renameColumn('image_url', 'image');
        });

        // Update special_rewards table: image_url -> image
        Schema::table('special_rewards', function (Blueprint $table) {
            $table->renameColumn('image_url', 'image');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse finalists table: photo -> photo_url
        Schema::table('finalists', function (Blueprint $table) {
            $table->renameColumn('photo', 'photo_url');
        });

        // Reverse merchandise_packages table: image -> image_url
        Schema::table('merchandise_packages', function (Blueprint $table) {
            $table->renameColumn('image', 'image_url');
        });

        // Reverse special_rewards table: image -> image_url
        Schema::table('special_rewards', function (Blueprint $table) {
            $table->renameColumn('image', 'image_url');
        });
    }
};
