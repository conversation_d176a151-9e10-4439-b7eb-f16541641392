<!DOCTYPE html>
<html lang="en">

<head>
    <title>Login</title>
    <!-- [Meta] -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Koreakaja CMS Admin - A modern dashboard for Korea Kaja Campaign" />
    <meta name="author" content="by.U" />
    <!-- [Favicon] icon -->
    <link rel="icon" href="{{ asset('favicon.png') }}
    <!-- [Font] Family -->
    <link rel=" stylesheet" href="{{ asset('vendor/dashboard') }}/assets/fonts/inter/inter.css" id="main-font-link" />
    <!-- [phosphor Icons] https://phosphoricons.com/ -->
    <link rel="stylesheet" href="{{ asset('vendor/dashboard') }}/assets/fonts/phosphor/duotone/style.css" />
    <!-- [Tabler Icons] https://tablericons.com -->
    <link rel="stylesheet" href="{{ asset('vendor/dashboard') }}/assets/fonts/tabler-icons.min.css" />
    <!-- [Feather Icons] https://feathericons.com -->
    <link rel="stylesheet" href="{{ asset('vendor/dashboard') }}/assets/fonts/feather.css" />
    <!-- [Font Awesome Icons] https://fontawesome.com/icons -->
    <link rel="stylesheet" href="{{ asset('vendor/dashboard') }}/assets/fonts/fontawesome.css" />
    <!-- [Material Icons] https://fonts.google.com/icons -->
    <link rel="stylesheet" href="{{ asset('vendor/dashboard') }}/assets/fonts/material.css" />
    <!-- [Template CSS Files] -->
    <link rel="stylesheet" href="{{ asset('vendor/dashboard') }}/assets/css/style.css" id="main-style-link" />
    <script src="{{ asset('vendor/dashboard') }}/assets/js/tech-stack.js"></script>
    <link rel="stylesheet" href="{{ asset('vendor/dashboard') }}/assets/css/style-preset.css" />
    <!-- [Bubblegum Theme] -->
    <link rel="stylesheet" href="{{ asset('assets/css/bubblegum-theme.css') }}" />

    <!-- [Ice Particles Animation CSS] -->
    <style>
        .ice-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 999;
            overflow: hidden;
        }

        .ice-particle {
            position: absolute;
            background: linear-gradient(45deg, #ffffff, #e3f2fd, #bbdefb);
            border-radius: 50%;
            opacity: 0.7;
            animation: fall linear infinite;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .ice-particle:nth-child(odd) {
            background: linear-gradient(45deg, #f0f8ff, #e1f5fe, #b3e5fc);
        }

        .ice-particle::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 60%;
            height: 60%;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
        }

        @keyframes fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 0;
            }

            10% {
                opacity: 0.7;
            }

            90% {
                opacity: 0.7;
            }

            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .login-container {
            position: relative;
            z-index: 10;
        }

        body {
            background: linear-gradient(135deg, #43c8f4 0%, #2196F3 50%, #1976D2 100%);
            min-height: 100vh;
        }
    </style>
</head><!-- [Head] end -->
<!-- [Body] Start -->

<body data-pc-preset="preset-1" data-pc-sidebar-caption="true" data-pc-layout="vertical" data-pc-direction="ltr"
    data-pc-theme_contrast="" data-pc-theme="light">
    <!-- [Ice Particles Container] -->
    <div class="ice-particles" id="ice-particles"></div>

    <!-- [ Pre-loader ] start -->
    <div class="loader-bg">
        <div class="loader-track">
            <div class="loader-fill"></div>
        </div>
    </div>
    <!-- [ Pre-loader ] End -->
    <div class="auth-main login-container">
        <div class="auth-wrapper v1">
            <div class="auth-form">
                <div class="card my-5 fade-in">
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <a href="#"><img src="{{ asset('logo.png') }}" style="width:300px;" class="bounce-in" /></a>
                        </div>
                        <div class="text-center mb-4">
                            <h3 class="fw-bold"
                                style="background: linear-gradient(45deg, #FF69B4, #DDA0DD); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                🎵 Koreakaja CMS Admin 🎵
                            </h3>
                        </div>

                        @include('sweetalert::alert')

                        <form action="{{ route('auth.submit') }}" method="post">
                            {{ csrf_field() }}
                            <div class="mb-4">
                                <label for="username" class="form-label fw-semibold">
                                    <i class="fas fa-user me-2" style="color: #FF69B4;"></i>Username
                                </label>
                                <input type="text" name="username" class="form-control" id="username"
                                    placeholder="Enter your username" required />
                            </div>
                            <div class="mb-4">
                                <label for="password" class="form-label fw-semibold">
                                    <i class="fas fa-lock me-2" style="color: #DDA0DD;"></i>Password
                                </label>
                                <input type="password" name="password" class="form-control" id="password"
                                    placeholder="Enter your password" required />
                            </div>
                            <div class="d-grid mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-heart" style="color: #FF69B4;"></i>
                                Made with love for Korea Kaja fans
                                <i class="fas fa-heart" style="color: #FF69B4;"></i>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
    <!-- Required Js -->
    <script src="{{ asset('vendor/dashboard') }}/assets/js/plugins/popper.min.js"></script>
    <script src="{{ asset('vendor/dashboard') }}/assets/js/plugins/simplebar.min.js"></script>
    <script src="{{ asset('vendor/dashboard') }}/assets/js/plugins/bootstrap.min.js"></script>
    <script src="{{ asset('vendor/dashboard') }}/assets/js/fonts/custom-font.js"></script>
    <script src="{{ asset('vendor/dashboard') }}/assets/js/pcoded.js"></script>
    <script src="{{ asset('vendor/dashboard') }}/assets/js/plugins/feather.min.js"></script>

    <!-- [Ice Particles JavaScript] -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Ice particles script loaded');
            const particlesContainer = document.getElementById('ice-particles');
            console.log('Particles container:', particlesContainer);
            const particleCount = 50;

            function createParticle() {
                const particle = document.createElement('div');
                particle.className = 'ice-particle';

                // Random size between 4px and 12px
                const size = Math.random() * 8 + 4;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';

                // Random horizontal position
                particle.style.left = Math.random() * 100 + '%';

                // Random animation duration between 3s and 8s
                const duration = Math.random() * 5 + 3;
                particle.style.animationDuration = duration + 's';

                // Random delay
                const delay = Math.random() * 2;
                particle.style.animationDelay = delay + 's';

                particlesContainer.appendChild(particle);

                // Remove particle after animation completes
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, (duration + delay) * 1000);
            }

            // Create initial particles
            for (let i = 0; i < particleCount; i++) {
                setTimeout(createParticle, Math.random() * 2000);
            }

            // Continuously create new particles
            setInterval(createParticle, 200);
        });
    </script>
</body>

</html>