<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SpecialReward;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class SpecialRewardController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = SpecialReward::query();

            // Filter by active status
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Filter by validity status
            if ($request->has('validity_status')) {
                switch ($request->validity_status) {
                    case 'active':
                        $query->notExpired();
                        break;
                    case 'expired':
                        $query->whereRaw('DATE_ADD(created_at, INTERVAL validity_period_days DAY) <= NOW()');
                        break;
                    case 'expiring_soon':
                        $query->expiringSoon();
                        break;
                }
            }

            // Search in reward name and data plan
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('reward_name', 'like', '%' . $search . '%')
                      ->orWhere('data_plan', 'like', '%' . $search . '%');
                });
            }

            // Filter by price range
            if ($request->has('price_min')) {
                $query->where('price', '>=', $request->price_min);
            }

            if ($request->has('price_max')) {
                $query->where('price', '<=', $request->price_max);
            }

            // Filter by validity period range
            if ($request->has('validity_min')) {
                $query->where('validity_period_days', '>=', $request->validity_min);
            }

            if ($request->has('validity_max')) {
                $query->where('validity_period_days', '<=', $request->validity_max);
            }

            // Sort by price or created_at
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            $perPage = $request->get('per_page', 15);
            $specialRewards = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $specialRewards,
                'message' => 'Special rewards retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve special rewards',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'image' => 'nullable|string',
                'reward_name' => 'required|string|max:255',
                'data_plan' => 'required|string|max:255',
                'validity_period_days' => 'required|integer|min:1',
                'price' => 'required|numeric|min:0',
                'description' => 'nullable|string',
                'product_url' => 'nullable|string|max:255|url',
                'is_active' => 'boolean'
            ]);

            $specialReward = SpecialReward::create($validated);

            return response()->json([
                'success' => true,
                'data' => $specialReward,
                'message' => 'Special reward created successfully'
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create special reward',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $specialReward = SpecialReward::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $specialReward,
                'message' => 'Special reward retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Special reward not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $specialReward = SpecialReward::findOrFail($id);

            $validated = $request->validate([
                'image' => 'nullable|string',
                'reward_name' => 'sometimes|required|string|max:255',
                'data_plan' => 'sometimes|required|string|max:255',
                'validity_period_days' => 'sometimes|required|integer|min:1',
                'price' => 'sometimes|required|numeric|min:0',
                'description' => 'nullable|string',
                'product_url' => 'nullable|string|max:255|url',
                'is_active' => 'boolean'
            ]);

            $specialReward->update($validated);

            return response()->json([
                'success' => true,
                'data' => $specialReward->fresh(),
                'message' => 'Special reward updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update special reward',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $specialReward = SpecialReward::findOrFail($id);
            $specialReward->delete();

            return response()->json([
                'success' => true,
                'message' => 'Special reward deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete special reward',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
