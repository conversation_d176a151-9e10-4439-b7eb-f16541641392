@extends('admin.layout.master')

@section('title', 'Create Special Quota')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <div class="pc-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('admin.events.index') }}">Home</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('admin.special-quotas.index') }}">Special
                                    Quotas</a></li>
                            <li class="breadcrumb-item" aria-current="page">Create</li>
                        </ul>
                    </div>
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h2 class="mb-0">Create New Special Quota</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row justify-content-center">
            <div class="col-xl-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-percentage me-2"></i>Special Quota Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.special-quotas.store') }}" method="POST">
                            @csrf

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="quota_name" class="form-label fw-semibold">
                                        <i class="fas fa-tag me-2 text-primary"></i>Quota Name
                                    </label>
                                    <input type="text" class="form-control @error('quota_name') is-invalid @enderror"
                                        id="quota_name" name="quota_name" value="{{ old('quota_name') }}"
                                        placeholder="Enter quota name">
                                    @error('quota_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-4">
                                    <label for="display_badge" class="form-label fw-semibold">
                                        <i class="fas fa-award me-2 text-warning"></i>Display Badge
                                    </label>
                                    <input type="text" class="form-control @error('display_badge') is-invalid @enderror"
                                        id="display_badge" name="display_badge" value="{{ old('display_badge') }}"
                                        placeholder="Enter display badge text">
                                    @error('display_badge')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="quota_volume" class="form-label fw-semibold">
                                        <i class="fas fa-database me-2 text-info"></i>Quota Volume
                                    </label>
                                    <input type="number"
                                        class="form-control @error('quota_volume') is-invalid @enderror"
                                        id="quota_volume" name="quota_volume" value="{{ old('quota_volume') }}"
                                        placeholder="Enter quota volume" min="1">
                                    @error('quota_volume')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Volume in units (e.g., GB, minutes, etc.)</div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <label for="validity_days" class="form-label fw-semibold">
                                        <i class="fas fa-calendar-days me-2 text-success"></i>Validity Days
                                    </label>
                                    <input type="number"
                                        class="form-control @error('validity_days') is-invalid @enderror"
                                        id="validity_days" name="validity_days" value="{{ old('validity_days') }}"
                                        placeholder="Enter validity in days" min="1">
                                    @error('validity_days')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Number of days the quota is valid</div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="normal_price" class="form-label fw-semibold">
                                        <i class="fas fa-money-bill me-2 text-secondary"></i>Normal Price
                                    </label>
                                    <input type="number"
                                        class="form-control @error('normal_price') is-invalid @enderror"
                                        id="normal_price" name="normal_price" value="{{ old('normal_price') }}"
                                        placeholder="Enter normal price" min="0" step="0.01">
                                    @error('normal_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Regular price in Rupiah</div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <label for="special_price" class="form-label fw-semibold">
                                        <i class="fas fa-tags me-2 text-danger"></i>Special Price
                                    </label>
                                    <input type="number"
                                        class="form-control @error('special_price') is-invalid @enderror"
                                        id="special_price" name="special_price" value="{{ old('special_price') }}"
                                        placeholder="Enter special price" min="0" step="0.01">
                                    @error('special_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Discounted price in Rupiah</div>
                                </div>
                            </div>

                            <!-- Dynamic Key-Value Details -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-list me-2 text-info"></i>Quota Details
                                </label>
                                <div class="text-muted small mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Add key-value pairs to provide specific details about the quota package
                                    (e.g., "Kuota Utuh" → "50 GB")
                                </div>
                                <div id="details-container">
                                    <!-- Default detail row will be added here by JavaScript -->
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-info" id="add-more-btn">
                                        <i class="fas fa-plus me-1"></i>Add More Details
                                    </button>
                                </div>
                                @error('details')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                                @error('details.*')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Product URL -->
                            <div class="mb-4">
                                <label for="product_url" class="form-label fw-semibold">
                                    <i class="fas fa-link me-2 text-primary"></i>Product URL
                                </label>
                                <input type="url" class="form-control @error('product_url') is-invalid @enderror"
                                    id="product_url" name="product_url" value="{{ old('product_url') }}"
                                    placeholder="https://example.com/product-page">
                                @error('product_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Optional external link to the product page or more information
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check form-switch">
                                    <input type="hidden" name="is_active" value="0">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                        value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label fw-semibold" for="is_active">
                                        <i class="fas fa-toggle-on me-2 text-success"></i>Active Status
                                    </label>
                                    <div class="form-text">Enable this quota to be available</div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ route('admin.special-quotas.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Create Quota
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    console.log('Special Quota Create Form Script Loading...');

    // Dynamic Key-Value Details Management
    let detailIndex = 0;

    // Get DOM elements
    const detailsContainer = document.getElementById('details-container');
    const addMoreBtn = document.getElementById('add-more-btn');
    const form = document.querySelector('form');

    if (!detailsContainer) {
        console.error('Details container not found');
        return;
    }

    if (!addMoreBtn) {
        console.error('Add more button not found');
        return;
    }

    // Utility functions
    function escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function createDetailRow(label = '', value = '', isFirst = false) {
        const index = detailIndex++;
        const escapedLabel = escapeHtml(label);
        const escapedValue = escapeHtml(value);

        const rowDiv = document.createElement('div');
        rowDiv.className = 'detail-row mb-3 p-3 border rounded bg-light';
        rowDiv.setAttribute('data-index', index);

        // Create remove button HTML - disabled for first row
        const removeButtonHtml = isFirst ?
            `<button type="button" class="btn btn-sm btn-outline-secondary d-block" disabled title="First detail cannot be removed">
                <i class="fas fa-trash"></i>
            </button>` :
            `<button type="button" class="btn btn-sm btn-outline-danger d-block remove-detail-btn" title="Remove Detail">
                <i class="fas fa-trash"></i>
            </button>`;

        rowDiv.innerHTML = `
            <div class="row align-items-center">
                <div class="col-md-4">
                    <label class="form-label fw-semibold small">Label</label>
                    <input type="text"
                           class="form-control form-control-sm"
                           name="details[${index}][label]"
                           value="${escapedLabel}"
                           placeholder="e.g., Kuota Utuh"
                           required>
                </div>
                <div class="col-md-6">
                    <label class="form-label fw-semibold small">Value</label>
                    <input type="text"
                           class="form-control form-control-sm"
                           name="details[${index}][value]"
                           value="${escapedValue}"
                           placeholder="e.g., 50 GB"
                           required>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">&nbsp;</label>
                    ${removeButtonHtml}
                </div>
            </div>
        `;

        return rowDiv;
    }

    function addDetailRow(label = '', value = '', isFirst = false) {
        console.log('Adding detail row:', { label, value, isFirst });

        try {
            const newRow = createDetailRow(label, value, isFirst);
            detailsContainer.appendChild(newRow);

            // Add event listener to the remove button (only for non-first rows)
            if (!isFirst) {
                const removeBtn = newRow.querySelector('.remove-detail-btn');
                if (removeBtn) {
                    removeBtn.addEventListener('click', function() {
                        removeDetailRow(newRow);
                    });
                }
            }

            console.log('Detail row added successfully');
            return true;
        } catch (error) {
            console.error('Error adding detail row:', error);
            return false;
        }
    }

    function removeDetailRow(rowElement) {
        rowElement.remove();
        console.log('Detail row removed');
    }

    // Initialize form
    function initializeForm() {
        console.log('Initializing form...');

        // Add the first default detail row (non-deletable)
        addDetailRow('Kuota Utuh', '', true);

        console.log('Default detail row added successfully');
    }

    // Event listeners
    addMoreBtn.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Add more button clicked');
        addDetailRow();
    });

    // Form validation
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('Form submission validation...');
            const detailRows = detailsContainer.querySelectorAll('.detail-row');

            if (detailRows.length === 0) {
                e.preventDefault();
                alert('Please add at least one quota detail.');
                return false;
            }

            // Validate that all detail rows have both label and value
            let hasEmptyFields = false;
            let emptyFieldCount = 0;

            detailRows.forEach((row, index) => {
                const labelInput = row.querySelector('input[name*="[label]"]');
                const valueInput = row.querySelector('input[name*="[value]"]');

                if (!labelInput || !valueInput) {
                    console.error(`Missing inputs in row ${index}`);
                    hasEmptyFields = true;
                    return;
                }

                const label = labelInput.value.trim();
                const value = valueInput.value.trim();

                if (!label || !value) {
                    hasEmptyFields = true;
                    emptyFieldCount++;
                    // Add visual feedback
                    if (!label) labelInput.classList.add('is-invalid');
                    if (!value) valueInput.classList.add('is-invalid');
                } else {
                    // Remove visual feedback if valid
                    labelInput.classList.remove('is-invalid');
                    valueInput.classList.remove('is-invalid');
                }
            });

            if (hasEmptyFields) {
                e.preventDefault();
                alert(`Please fill in all label and value fields. Found ${emptyFieldCount} empty field(s).`);
                return false;
            }

            console.log('Form validation passed');
        });
    }

    // Price validation
    const normalPriceInput = document.getElementById('normal_price');
    const specialPriceInput = document.getElementById('special_price');

    if (normalPriceInput && specialPriceInput) {
        function validatePrices() {
            const normalPrice = parseFloat(normalPriceInput.value) || 0;
            const specialPrice = parseFloat(specialPriceInput.value) || 0;

            if (specialPrice >= normalPrice && normalPrice > 0) {
                specialPriceInput.setCustomValidity('Special price must be less than normal price');
            } else {
                specialPriceInput.setCustomValidity('');
            }
        }

        specialPriceInput.addEventListener('input', validatePrices);
        normalPriceInput.addEventListener('input', validatePrices);
    }

    // Initialize the form
    initializeForm();

    console.log('Special Quota Create Form initialized successfully');
});
</script>
@stop