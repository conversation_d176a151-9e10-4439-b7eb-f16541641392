@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-gift me-3"></i>Special Rewards Management
                            </h2>
                            <p class="text-white mb-0">Manage promotional rewards with images and validity periods</p>
                        </div>
                        <a href="{{ route('admin.special-rewards.create') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-plus me-2"></i>Add New Reward
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.special-rewards.index') }}">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label fw-semibold">Search</label>
                                <input type="text" name="search" class="form-control"
                                    placeholder="Search by reward name..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Min Price</label>
                                <input type="number" name="price_min" class="form-control" placeholder="0" step="0.01"
                                    value="{{ request('price_min') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Max Price</label>
                                <input type="number" name="price_max" class="form-control" placeholder="1000"
                                    step="0.01" value="{{ request('price_max') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Status</label>
                                <select name="is_active" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="1" {{ request('is_active')==='1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ request('is_active')==='0' ? 'selected' : '' }}>Inactive
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filter
                                    </button>
                                    <button type="button" id="clear-filters" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Special Rewards List
                        <span class="badge bg-light text-dark ms-2">{{ $specialRewards->total() }} Total</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if($specialRewards->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Reward</th>
                                    <th>Price</th>
                                    <th>Validity Period</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($specialRewards as $reward)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($reward->image_url)
                                            <img src="{{ $reward->image_url }}" alt="{{ $reward->reward_name }}"
                                                class="rounded me-3"
                                                style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                            <div class="rounded bg-secondary d-flex align-items-center justify-content-center me-3"
                                                style="width: 40px; height: 40px;">
                                                <i class="fas fa-gift text-white"></i>
                                            </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $reward->reward_name }}</div>
                                                @if($reward->description)
                                                <small class="text-muted">{{ Str::limit($reward->description, 40)
                                                    }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($reward->price)
                                        <div class="fw-bold text-success">${{ number_format($reward->price, 2) }}</div>
                                        @else
                                        <span class="text-muted">Free</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="small">
                                            <div><strong>Period:</strong> {{ $reward->validity_period_days }} days</div>
                                            <div><strong>Expires:</strong> {{ $reward->expiration_date->format('M d, Y')
                                                }}</div>
                                        </div>
                                        @php
                                        $status = $reward->validity_status;
                                        @endphp
                                        @if($status === 'expired')
                                        <span class="badge bg-danger">Expired</span>
                                        @elseif($status === 'expiring_soon')
                                        <span class="badge bg-warning">Expiring Soon</span>
                                        @else
                                        <span class="badge bg-success">{{ $reward->formatted_validity }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($reward->is_active)
                                        <span class="badge bg-success">Active</span>
                                        @else
                                        <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.special-rewards.show', $reward->id) }}"
                                                class="btn btn-sm btn-info" title="View Reward Details">
                                                <i class="fas fa-eye me-1"></i>
                                                <span class="d-none d-md-inline">View</span>
                                            </a>
                                            <a href="{{ route('admin.special-rewards.edit', $reward->id) }}"
                                                class="btn btn-sm btn-warning" title="Edit Reward">
                                                <i class="fas fa-edit me-1"></i>
                                                <span class="d-none d-md-inline">Edit</span>
                                            </a>
                                            <form action="{{ route('admin.special-rewards.destroy', $reward->id) }}"
                                                method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger"
                                                    onclick="return confirm('Are you sure you want to delete this reward?')"
                                                    title="Delete Reward">
                                                    <i class="fas fa-trash me-1"></i>
                                                    <span class="d-none d-md-inline">Delete</span>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $specialRewards->withQueryString()->links() }}
                    </div>
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-gift fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No special rewards found</h5>
                        <p class="text-muted">Start by adding your first reward.</p>
                        <a href="{{ route('admin.special-rewards.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Reward
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when filters change
        const filterForm = document.querySelector('form[action*="special-rewards.index"]');
        const selectFilters = filterForm.querySelectorAll('select[name="is_active"]');
        const priceInputs = filterForm.querySelectorAll('input[name="price_min"], input[name="price_max"]');

        selectFilters.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });

        // Add search delay for better UX
        const searchInput = filterForm.querySelector('input[name="search"]');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterForm.submit();
            }, 500); // 500ms delay
        });

        // Price filter with delay
        priceInputs.forEach(input => {
            let priceTimeout;
            input.addEventListener('input', function() {
                clearTimeout(priceTimeout);
                priceTimeout = setTimeout(() => {
                    filterForm.submit();
                }, 800); // 800ms delay for price inputs
            });
        });

        // Clear filters functionality
        const clearBtn = document.getElementById('clear-filters');
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                filterForm.querySelectorAll('input, select').forEach(input => {
                    if (input.type === 'text' || input.type === 'search' || input.type === 'number') {
                        input.value = '';
                    } else if (input.tagName === 'SELECT') {
                        input.selectedIndex = 0;
                    }
                });
                filterForm.submit();
            });
        }
    });
</script>
@endsection