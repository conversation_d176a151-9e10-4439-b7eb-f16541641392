<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\EventFilterTrait;
use App\Http\Traits\FileUploadTrait;
use App\Models\MerchandisePackage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;

class MerchandisePackageController extends Controller
{
    use EventFilterTrait, FileUploadTrait;
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (!in_array(Auth::user()->role, ['superadmin', 'admin'])) {
                abort(403, 'Access Denied. Only Admin roles are allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = MerchandisePackage::query();

        // Apply event filter
        $query = $this->applyEventFilter($query);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('product_name', 'like', '%' . $search . '%')
                  ->orWhere('data_plan', 'like', '%' . $search . '%');
            });
        }

        // Filter by active status
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by price range
        if ($request->has('price_min') && !empty($request->price_min)) {
            $query->where('price', '>=', $request->price_min);
        }

        if ($request->has('price_max') && !empty($request->price_max)) {
            $query->where('price', '<=', $request->price_max);
        }

        $merchandisePackages = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.merchandise-packages.index', compact('merchandisePackages'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.merchandise-packages.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if an event is selected
        $redirectResponse = $this->requireActiveEvent();
        if ($redirectResponse) {
            Alert::warning('Warning', 'Please select an event first before creating new content.');
            return $redirectResponse;
        }

        $validated = $request->validate([
            'product_name' => 'required|string|max:255',
            'data_plan' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'image' => array_merge(['nullable'], $this->getImageValidationRules()),
            'duration_days' => 'required|integer|min:1',
            'description' => 'nullable|string',
            'product_url' => 'nullable|string|max:255|url',
            'is_active' => 'boolean'
        ]);

        // Handle file upload
        $this->handleFileUpload($validated, 'image', 'merchandise');

        // Set event_id for new record
        $this->setEventIdForNewRecord($validated);

        MerchandisePackage::create($validated);
        Alert::success('Success', 'Merchandise package created successfully!');

        return redirect()->route('admin.merchandise-packages.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $merchandisePackage = MerchandisePackage::findOrFail($id);
        return view('admin.merchandise-packages.show', compact('merchandisePackage'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $merchandisePackage = MerchandisePackage::findOrFail($id);
        return view('admin.merchandise-packages.edit', compact('merchandisePackage'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $merchandisePackage = MerchandisePackage::findOrFail($id);

        $validated = $request->validate([
            'product_name' => 'required|string|max:255',
            'data_plan' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'image' => array_merge(['nullable'], $this->getImageValidationRules()),
            'duration_days' => 'required|integer|min:1',
            'description' => 'nullable|string',
            'product_url' => 'nullable|string|max:255|url',
            'is_active' => 'boolean'
        ]);

        // Handle file update
        $this->handleFileUpdate($validated, 'image', $merchandisePackage->image, 'merchandise');

        $merchandisePackage->update($validated);
        Alert::success('Success', 'Merchandise package updated successfully!');

        return redirect()->route('admin.merchandise-packages.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $merchandisePackage = MerchandisePackage::findOrFail($id);
            $merchandisePackage->delete();
            Alert::success('Success', 'Merchandise package deleted successfully!');
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to delete merchandise package!');
        }

        return redirect()->route('admin.merchandise-packages.index');
    }
}
