@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-calendar-alt me-3"></i>Event Schedules Management
                            </h2>
                            <p class="text-white mb-0">Manage Koreakaja event schedules across different cities</p>
                        </div>
                        <a href="{{ route('admin.event-schedules.create') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-plus me-2"></i>Add New Event
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.event-schedules.index') }}">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label fw-semibold">Search</label>
                                <input type="text" name="search" class="form-control"
                                    placeholder="Search by city, location, or description..."
                                    value="{{ request('search') }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">City</label>
                                <select name="city" class="form-select">
                                    <option value="">All Cities</option>
                                    @foreach($cities as $city)
                                    <option value="{{ $city }}" {{ request('city')==$city ? 'selected' : '' }}>
                                        {{ $city }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">Status</label>
                                <select name="is_active" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="1" {{ request('is_active')==='1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ request('is_active')==='0' ? 'selected' : '' }}>Inactive
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Event Schedules List
                        <span class="badge bg-primary ms-2">{{ $eventSchedules->total() }} Total</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if($eventSchedules->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>City</th>
                                    <th>Event Date Range</th>
                                    <th>Location</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($eventSchedules as $schedule)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $schedule->city }}</div>
                                    </td>
                                    <td>
                                        <div class="fw-semibold">{{ $schedule->formatted_date_range }}</div>
                                        <small class="text-muted">{{ $schedule->event_date->format('l') }}</small>
                                    </td>
                                    <td>
                                        <div>{{ $schedule->location }}</div>
                                        @if($schedule->description)
                                        <small class="text-muted">{{ Str::limit($schedule->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="fw-semibold">{{ $schedule->duration_in_days }} day{{
                                            $schedule->duration_in_days > 1 ? 's' : '' }}</div>
                                        @if($schedule->is_ongoing)
                                        <small class="text-success">Ongoing</small>
                                        @elseif($schedule->is_upcoming)
                                        <small class="text-primary">Upcoming</small>
                                        @else
                                        <small class="text-muted">Past</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($schedule->is_active)
                                        <span class="badge bg-success">Active</span>
                                        @else
                                        <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.event-schedules.show', $schedule->id) }}"
                                                class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye me-1"></i>
                                                <span class="d-none d-md-inline">View</span>
                                            </a>
                                            <a href="{{ route('admin.event-schedules.edit', $schedule->id) }}"
                                                class="btn btn-sm btn-warning" title="Edit Event">
                                                <i class="fas fa-edit me-1"></i>
                                                <span class="d-none d-md-inline">Edit</span>
                                            </a>
                                            <form action="{{ route('admin.event-schedules.destroy', $schedule->id) }}"
                                                method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger"
                                                    onclick="return confirm('Are you sure you want to delete this event schedule?')"
                                                    title="Delete Event">
                                                    <i class="fas fa-trash me-1"></i>
                                                    <span class="d-none d-md-inline">Delete</span>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $eventSchedules->withQueryString()->links() }}
                    </div>
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No event schedules found</h5>
                        <p class="text-muted">Start by creating your first event schedule.</p>
                        <a href="{{ route('admin.event-schedules.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Event Schedule
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change
    const filterForm = document.querySelector('form[action*="event-schedules.index"]');
    const selectFilters = filterForm.querySelectorAll('select[name="city"], select[name="is_active"]');

    selectFilters.forEach(select => {
        select.addEventListener('change', function() {
            filterForm.submit();
        });
    });

    // Add search delay for better UX
    const searchInput = filterForm.querySelector('input[name="search"]');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            filterForm.submit();
        }, 500); // 500ms delay
    });

    // Clear filters functionality
    const clearBtn = document.getElementById('clear-filters');
    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            filterForm.querySelectorAll('input, select').forEach(input => {
                if (input.type === 'text' || input.type === 'search') {
                    input.value = '';
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                }
            });
            filterForm.submit();
        });
    }
});
</script>
@endsection