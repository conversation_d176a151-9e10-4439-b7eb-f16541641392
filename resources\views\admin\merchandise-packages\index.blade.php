@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-box me-3"></i>Merchandise Packages Management
                            </h2>
                            <p class="text-white mb-0">Manage product offerings with data plans and pricing</p>
                        </div>
                        <a href="{{ route('admin.merchandise-packages.create') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-plus me-2"></i>Add New Package
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.merchandise-packages.index') }}">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label fw-semibold">Search</label>
                                <input type="text" name="search" class="form-control"
                                    placeholder="Search by product name or data plan..."
                                    value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Min Price</label>
                                <input type="number" name="price_min" class="form-control" placeholder="0" step="0.01"
                                    value="{{ request('price_min') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Max Price</label>
                                <input type="number" name="price_max" class="form-control" placeholder="1000"
                                    step="0.01" value="{{ request('price_max') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Status</label>
                                <select name="is_active" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="1" {{ request('is_active')==='1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ request('is_active')==='0' ? 'selected' : '' }}>Inactive
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Merchandise Packages List
                        <span class="badge bg-light text-dark ms-2">{{ $merchandisePackages->total() }} Total</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if($merchandisePackages->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Data Plan</th>
                                    <th>Price</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($merchandisePackages as $package)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($package->image_url)
                                            <img src="{{ $package->image_url }}" alt="{{ $package->product_name }}"
                                                class="rounded me-3"
                                                style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                            <div class="rounded bg-secondary d-flex align-items-center justify-content-center me-3"
                                                style="width: 40px; height: 40px;">
                                                <i class="fas fa-box text-white"></i>
                                            </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $package->product_name }}</div>
                                                @if($package->description)
                                                <small class="text-muted">{{ Str::limit($package->description, 40)
                                                    }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $package->data_plan }}</span>
                                    </td>
                                    <td>
                                        <div class="fw-bold text-success">${{ number_format($package->price, 2) }}</div>
                                    </td>
                                    <td>
                                        <span class="fw-semibold">{{ $package->duration_days }} days</span>
                                    </td>
                                    <td>
                                        @if($package->is_active)
                                        <span class="badge bg-success">Active</span>
                                        @else
                                        <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.merchandise-packages.show', $package->id) }}"
                                                class="btn btn-sm btn-info" title="View Package Details">
                                                <i class="fas fa-eye me-1"></i>
                                                <span class="d-none d-md-inline">View</span>
                                            </a>
                                            <a href="{{ route('admin.merchandise-packages.edit', $package->id) }}"
                                                class="btn btn-sm btn-warning" title="Edit Package">
                                                <i class="fas fa-edit me-1"></i>
                                                <span class="d-none d-md-inline">Edit</span>
                                            </a>
                                            <form
                                                action="{{ route('admin.merchandise-packages.destroy', $package->id) }}"
                                                method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger"
                                                    onclick="return confirm('Are you sure you want to delete this package?')"
                                                    title="Delete Package">
                                                    <i class="fas fa-trash me-1"></i>
                                                    <span class="d-none d-md-inline">Delete</span>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $merchandisePackages->withQueryString()->links() }}
                    </div>
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No merchandise packages found</h5>
                        <p class="text-muted">Start by adding your first package.</p>
                        <a href="{{ route('admin.merchandise-packages.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Package
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when filters change
        const filterForm = document.querySelector('form[action*="merchandise-packages.index"]');
        const selectFilters = filterForm.querySelectorAll('select[name="is_active"]');
        const priceInputs = filterForm.querySelectorAll('input[name="price_min"], input[name="price_max"]');

        selectFilters.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });

        // Add search delay for better UX
        const searchInput = filterForm.querySelector('input[name="search"]');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterForm.submit();
            }, 500); // 500ms delay
        });

        // Price filter with delay
        priceInputs.forEach(input => {
            let priceTimeout;
            input.addEventListener('input', function() {
                clearTimeout(priceTimeout);
                priceTimeout = setTimeout(() => {
                    filterForm.submit();
                }, 800); // 800ms delay for price inputs
            });
        });

        // Clear filters functionality
        const clearBtn = document.getElementById('clear-filters');
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                filterForm.querySelectorAll('input, select').forEach(input => {
                    if (input.type === 'text' || input.type === 'search' || input.type === 'number') {
                        input.value = '';
                    } else if (input.tagName === 'SELECT') {
                        input.selectedIndex = 0;
                    }
                });
                filterForm.submit();
            });
        }
    });
</script>
@endsection