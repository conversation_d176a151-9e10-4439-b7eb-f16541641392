<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Traits\EventFilterTrait;
use App\Models\Faq;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class FaqController extends Controller
{
    use EventFilterTrait;

    /**
     * Display a listing of the resource.
     *
     * Available query parameters:
     * - campaign_section: Filter by specific campaign section
     * - section: Alias for campaign_section
     * - is_active: Filter by active status (true/false)
     * - search: Search in question and answer text
     * - per_page: Number of items per page (default: 15)
     * - sort_by: Sort field (sort_order, created_at, question) - default: sort_order
     * - sort_direction: Sort direction (asc, desc) - default: asc for sort_order, desc for others
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Faq::query();

            // For public API access, no event filtering needed

            // Filter by campaign section if provided (support both 'campaign_section' and 'section' params)
            $section = $request->get('campaign_section') ?: $request->get('section');
            if ($section) {
                $query->where('campaign_section', $section);
            }

            // Filter by active status (default to active only for public API)
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            } else {
                // Default to active FAQs only for public consumption
                $query->where('is_active', true);
            }

            // Search in question and answer
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('question', 'like', '%' . $search . '%')
                      ->orWhere('answer', 'like', '%' . $search . '%');
                });
            }

            // Sorting options
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortDirection = $request->get('sort_direction');

            // Set default sort direction based on field
            if (!$sortDirection) {
                $sortDirection = $sortBy === 'sort_order' ? 'asc' : 'desc';
            }

            // Validate sort fields
            $allowedSortFields = ['sort_order', 'created_at', 'updated_at', 'question'];
            if (in_array($sortBy, $allowedSortFields)) {
                $query->orderBy($sortBy, $sortDirection);
            } else {
                // Default sorting
                $query->orderBy('sort_order', 'asc')->orderBy('created_at', 'desc');
            }

            $perPage = min($request->get('per_page', 15), 100); // Max 100 items per page
            $faqs = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $faqs,
                'message' => 'FAQs retrieved successfully',
                'filters_applied' => [
                    'campaign_section' => $section,
                    'is_active' => $request->get('is_active', 'true (default)'),
                    'search' => $request->get('search'),
                    'sort_by' => $sortBy,
                    'sort_direction' => $sortDirection
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve FAQs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available campaign sections
     */
    public function sections(Request $request): JsonResponse
    {
        try {
            $query = Faq::query();

            // Only get sections from active FAQs by default
            if (!$request->has('include_inactive') || !$request->boolean('include_inactive')) {
                $query->where('is_active', true);
            }

            $sections = $query->whereNotNull('campaign_section')
                            ->where('campaign_section', '!=', '')
                            ->distinct()
                            ->pluck('campaign_section')
                            ->sort()
                            ->values();

            return response()->json([
                'success' => true,
                'data' => $sections,
                'message' => 'Campaign sections retrieved successfully',
                'total_sections' => $sections->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve campaign sections',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'question' => 'required|string',
                'answer' => 'required|string',
                'campaign_section' => 'nullable|string|max:255',
                'sort_order' => 'integer|min:0',
                'is_active' => 'boolean'
            ]);

            $faq = Faq::create($validated);

            return response()->json([
                'success' => true,
                'data' => $faq,
                'message' => 'FAQ created successfully'
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create FAQ',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $faq = Faq::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $faq,
                'message' => 'FAQ retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'FAQ not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $faq = Faq::findOrFail($id);

            $validated = $request->validate([
                'question' => 'sometimes|required|string',
                'answer' => 'sometimes|required|string',
                'campaign_section' => 'nullable|string|max:255',
                'sort_order' => 'integer|min:0',
                'is_active' => 'boolean'
            ]);

            $faq->update($validated);

            return response()->json([
                'success' => true,
                'data' => $faq->fresh(),
                'message' => 'FAQ updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update FAQ',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $faq = Faq::findOrFail($id);
            $faq->delete();

            return response()->json([
                'success' => true,
                'message' => 'FAQ deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete FAQ',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
