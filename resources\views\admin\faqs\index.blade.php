@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" style="background: linear-gradient(135deg, #DDA0DD 0%, #BA55D3 100%); border: none;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-question-circle me-3"></i>FAQ Management
                            </h2>
                            <p class="text-white mb-0"><PERSON><PERSON> frequently asked questions for Korea Kaja campaigns</p>
                        </div>
                        <a href="{{ route('admin.faqs.create') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-plus me-2"></i>Add New FAQ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.faqs.index') }}">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label fw-semibold">Search</label>
                                <input type="text" name="search" class="form-control"
                                    placeholder="Search in questions and answers..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">Campaign Section</label>
                                <select name="campaign_section" class="form-select">
                                    <option value="">All Sections</option>
                                    @foreach($campaignSections as $section)
                                    <option value="{{ $section }}" {{ request('campaign_section')==$section ? 'selected'
                                        : '' }}>
                                        {{ $section }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">Status</label>
                                <select name="is_active" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="1" {{ request('is_active')==='1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ request('is_active')==='0' ? 'selected' : '' }}>Inactive
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>FAQ List
                        <span class="badge bg-primary ms-2">{{ $faqs->total() }} Total</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if($faqs->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Sort Order</th>
                                    <th>Question</th>
                                    <th>Campaign Section</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($faqs as $faq)
                                <tr>
                                    <td>
                                        <span class="badge bg-info">{{ $faq->sort_order }}</span>
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ Str::limit($faq->question, 60) }}</div>
                                        <small class="text-muted">{{ Str::limit($faq->answer, 80) }}</small>
                                    </td>
                                    <td>
                                        @if($faq->campaign_section)
                                        <span class="badge bg-secondary">{{ $faq->campaign_section }}</span>
                                        @else
                                        <span class="text-muted">General</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($faq->is_active)
                                        <span class="badge bg-success">Active</span>
                                        @else
                                        <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.faqs.show', $faq->id) }}"
                                                class="btn btn-sm btn-info" title="View FAQ Details">
                                                <i class="fas fa-eye me-1"></i>
                                                <span class="d-none d-md-inline">View</span>
                                            </a>
                                            <a href="{{ route('admin.faqs.edit', $faq->id) }}"
                                                class="btn btn-sm btn-warning" title="Edit FAQ">
                                                <i class="fas fa-edit me-1"></i>
                                                <span class="d-none d-md-inline">Edit</span>
                                            </a>
                                            <form action="{{ route('admin.faqs.destroy', $faq->id) }}" method="POST"
                                                class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger"
                                                    onclick="return confirm('Are you sure you want to delete this FAQ?')"
                                                    title="Delete FAQ">
                                                    <i class="fas fa-trash me-1"></i>
                                                    <span class="d-none d-md-inline">Delete</span>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $faqs->withQueryString()->links() }}
                    </div>
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No FAQs found</h5>
                        <p class="text-muted">Start by creating your first FAQ.</p>
                        <a href="{{ route('admin.faqs.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add FAQ
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when filters change
        const filterForm = document.querySelector('form[action*="faqs.index"]');
        const selectFilters = filterForm.querySelectorAll('select[name="campaign_section"], select[name="is_active"]');

        selectFilters.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });

        // Add search delay for better UX
        const searchInput = filterForm.querySelector('input[name="search"]');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterForm.submit();
            }, 500); // 500ms delay
        });

        // Clear filters functionality
        const clearBtn = document.getElementById('clear-filters');
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                filterForm.querySelectorAll('input, select').forEach(input => {
                    if (input.type === 'text' || input.type === 'search') {
                        input.value = '';
                    } else if (input.tagName === 'SELECT') {
                        input.selectedIndex = 0;
                    }
                });
                filterForm.submit();
            });
        }
    });
</script>
@endsection