@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-edit me-3"></i>Edit FAQ
                            </h2>
                            <p class="text-white mb-0">Update frequently asked question</p>
                        </div>
                        <div>
                            <a href="{{ route('admin.faqs.show', $faq->id) }}" class="btn btn-light me-2">
                                <i class="fas fa-eye me-2"></i>View
                            </a>
                            <a href="{{ route('admin.faqs.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>FAQ Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.faqs.update', $faq->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-4">
                            <label for="question" class="form-label fw-semibold">
                                <i class="fas fa-question me-2 text-primary"></i>Question *
                            </label>
                            <textarea class="form-control @error('question') is-invalid @enderror" id="question"
                                name="question" rows="3" placeholder="Enter the frequently asked question"
                                required>{{ old('question', $faq->question) }}</textarea>
                            @error('question')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="answer" class="form-label fw-semibold">
                                <i class="fas fa-comment me-2 text-success"></i>Answer *
                            </label>
                            <textarea class="form-control @error('answer') is-invalid @enderror" id="answer"
                                name="answer" rows="6" placeholder="Enter the detailed answer"
                                required>{{ old('answer', $faq->answer) }}</textarea>
                            @error('answer')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="campaign_section" class="form-label fw-semibold">
                                    <i class="fas fa-tag me-2 text-info"></i>Campaign Section
                                </label>
                                <input type="text" class="form-control @error('campaign_section') is-invalid @enderror"
                                    id="campaign_section" name="campaign_section"
                                    value="{{ old('campaign_section', $faq->campaign_section) }}"
                                    placeholder="e.g., General, Contest, Rewards, etc.">
                                @error('campaign_section')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Optional: Categorize this FAQ by campaign section</div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="sort_order" class="form-label fw-semibold">
                                    <i class="fas fa-sort-numeric-up me-2 text-warning"></i>Sort Order
                                </label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                                    id="sort_order" name="sort_order" value="{{ old('sort_order', $faq->sort_order) }}"
                                    min="0" placeholder="0">
                                @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Lower numbers appear first</div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                    value="1" {{ old('is_active', $faq->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label fw-semibold" for="is_active">
                                    <i class="fas fa-toggle-on me-2 text-success"></i>Active Status
                                </label>
                                <div class="form-text">Enable this FAQ to be visible to users</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.faqs.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update FAQ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textareas
    function autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
    }
    
    const questionTextarea = document.getElementById('question');
    const answerTextarea = document.getElementById('answer');
    
    questionTextarea.addEventListener('input', function() {
        autoResize(this);
    });
    
    answerTextarea.addEventListener('input', function() {
        autoResize(this);
    });
});
</script>
@endsection