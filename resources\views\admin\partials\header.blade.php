<!-- [ Sidebar Menu ] end -->
<!-- [ Header Topbar ] start -->
<header class="pc-header">
    <div class="header-wrapper">
        <!-- [Mobile Media Block] start -->
        <div class="me-auto pc-mob-drp">
            <ul class="list-unstyled">
                <!-- ======= Menu collapse Icon ===== -->
                <li class="pc-h-item pc-sidebar-collapse">
                    <a href="#" class="pc-head-link ms-0" id="sidebar-hide"><i class="ti ti-menu-2"></i></a>
                </li>
                <li class="pc-h-item pc-sidebar-popup">
                    <a href="#" class="pc-head-link ms-0" id="mobile-collapse"><i class="ti ti-menu-2"></i></a>
                </li>
            </ul>
        </div>
        <!-- [Mobile Media Block end] -->
        <div class="ms-auto">
            <ul class="list-unstyled">
                <!-- Event Switcher -->
                <li class="dropdown pc-h-item">
                    <a class="pc-head-link dropdown-toggle arrow-none me-0" data-bs-toggle="dropdown" href="#"
                        role="button" aria-haspopup="false" aria-expanded="false">
                        <i class="fas fa-star me-2"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end pc-h-dropdown">
                        <h6 class="dropdown-header">Switch Event</h6>
                        <form action="{{ route('admin.events.set-active') }}" method="POST" class="d-inline">
                            @csrf
                            <input type="hidden" name="event_id" value="">
                            <button type="submit"
                                class="dropdown-item {{ !session('active_event_id') ? 'active' : '' }}">
                                <i class="fas fa-globe me-2"></i>All Events
                            </button>
                        </form>
                        <div class="dropdown-divider"></div>
                        @foreach(\App\Models\Event::where('is_active', 1)->orderBy('period', 'desc')->get() as $event)
                        <form action="{{ route('admin.events.set-active') }}" method="POST" class="d-inline">
                            @csrf
                            <input type="hidden" name="event_id" value="{{ $event->id }}">
                            <button type="submit"
                                class="dropdown-item {{ session('active_event_id') == $event->id ? 'active' : '' }}">
                                <i class="fas fa-star me-2"></i>{{ $event->event_name }} ({{ $event->period }})
                            </button>
                        </form>
                        @endforeach
                    </div>
                </li>
            </ul>
        </div>
    </div>
</header>