<div class="form-group{{ $errors->has('name') ? 'has-error' : '' }}">
    {!! Form::label('name', 'Name', ['class' => 'control-label']) !!}
    {!! Form::text(
    'name',
    null,
    '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('name', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('username') ? 'has-error' : '' }}">
    {!! Form::label('username', 'Username', ['class' => 'control-label']) !!}
    {!! Form::text(
    'username',
    null,
    '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('username', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('email') ? 'has-error' : '' }}">
    {!! Form::label('email', 'Email', ['class' => 'control-label']) !!}
    {!! Form::email(
    'email',
    null,
    '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('email', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('password') ? 'has-error' : '' }}">
    {!! Form::label('password', 'Password', ['class' => 'control-label']) !!}
    {!! Form::password(
    'password',
    '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('password', '<p class="help-block">:message</p>') !!}
</div>
<div class="form-group{{ $errors->has('role') ? 'has-error' : '' }}">
    {!! Form::label('role', 'Role', ['class' => 'control-label']) !!}
    {!! Form::select(
    'role',
    ['superadmin' => 'Super Admin', 'admin' => 'Admin'],
    null,
    '' == 'required' ? ['class' => 'form-control', 'required' => 'required'] : ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('role', '<p class="help-block">:message</p>') !!}
</div>

@if(auth()->user()->role === 'superadmin')
<div class="form-group{{ $errors->has('event_id') ? 'has-error' : '' }}">
    {!! Form::label('event_id', 'Assigned Event (for Admin users)', ['class' => 'control-label']) !!}
    {!! Form::select(
    'event_id',
    ['' => 'No specific event (access all)'] + \App\Models\Event::where('is_active', true)->pluck('event_name',
    'id')->toArray(),
    null,
    ['class' => 'form-control'],
    ) !!}
    {!! $errors->first('event_id', '<p class="help-block">:message</p>') !!}
    <small class="form-text text-muted">Leave empty for Super Admin users or to allow access to all events</small>
</div>
@endif

<br>
<div class="form-group" align="right">
    {!! Form::submit($formMode === 'edit' ? 'Update' : 'Create', ['class' => 'btn btn-primary']) !!}
    {!! Form::reset('Reset', ['class' => 'btn btn-warning']) !!}
    <a href="#" onClick="javascript:history.go(-1)" class="btn btn-danger">Cancel and Back</a>
</div>