<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataTables Conflict Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>DataTables Conflict Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <h4>DataTable (should work)</h4>
                <table id="pc-dt-simple" class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Test 1</td>
                            <td>Value 1</td>
                        </tr>
                        <tr>
                            <td>Test 2</td>
                            <td>Value 2</td>
                        </tr>
                        <tr>
                            <td>Test 3</td>
                            <td>Value 3</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="col-md-6">
                <h4>Dynamic Form (should also work)</h4>
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-semibold text-info">Key-Value Details</span>
                            <button type="button" class="btn btn-sm btn-info" id="add-detail-btn">
                                <i class="fas fa-plus me-1"></i>Add Detail
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="details-container">
                            <!-- Details will be added here by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <div class="alert alert-info">
                <h5>Test Results:</h5>
                <div id="test-results">
                    <p>Loading...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Load DataTables library -->
    <script src="/vendor/dashboard/assets/js/plugins/simple-datatables.js"></script>
    
    <script>
        console.log('DataTables Conflict Test Loading...');
        
        let detailIndex = 0;
        let testResults = [];
        
        function logResult(test, result, details = '') {
            testResults.push({ test, result, details });
            updateResultsDisplay();
        }
        
        function updateResultsDisplay() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(r => 
                `<p><strong>${r.test}:</strong> <span class="badge ${r.result ? 'bg-success' : 'bg-danger'}">${r.result ? 'PASS' : 'FAIL'}</span> ${r.details}</p>`
            ).join('');
        }

        function createDetailRow(label = '', value = '', index = null) {
            if (index === null) {
                index = detailIndex++;
            }

            return `
                <div class="detail-row mb-3 p-3 border rounded bg-light" data-index="${index}">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <input type="text" class="form-control form-control-sm" value="${label}" placeholder="Label">
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control form-control-sm" value="${value}" placeholder="Value">
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-sm btn-outline-danger remove-detail-btn">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function addDetailRow(label = '', value = '') {
            try {
                const container = document.getElementById('details-container');
                if (!container) {
                    logResult('Add Detail Row', false, 'Container not found');
                    return false;
                }
                
                const newRow = createDetailRow(label, value);
                container.insertAdjacentHTML('beforeend', newRow);
                
                const newRowElement = container.lastElementChild;
                const removeBtn = newRowElement.querySelector('.remove-detail-btn');
                if (removeBtn) {
                    removeBtn.addEventListener('click', function() {
                        newRowElement.remove();
                    });
                }
                
                logResult('Add Detail Row', true, `Added row with label: "${label}"`);
                return true;
            } catch (error) {
                logResult('Add Detail Row', false, `Error: ${error.message}`);
                return false;
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, running tests...');
            
            // Test 1: DataTables initialization
            try {
                const tableElement = document.querySelector("#pc-dt-simple");
                if (tableElement) {
                    const dataTable = new simpleDatatables.DataTable("#pc-dt-simple", {
                        sortable: false,
                        perPage: 5,
                    });
                    logResult('DataTables Initialization', true, 'DataTable created successfully');
                } else {
                    logResult('DataTables Initialization', false, 'Table element not found');
                }
            } catch (error) {
                logResult('DataTables Initialization', false, `Error: ${error.message}`);
            }
            
            // Test 2: Dynamic form initialization
            try {
                addDetailRow('Test Label', 'Test Value');
                logResult('Dynamic Form Initialization', true, 'Form initialized successfully');
            } catch (error) {
                logResult('Dynamic Form Initialization', false, `Error: ${error.message}`);
            }
            
            // Test 3: Add button functionality
            const addBtn = document.getElementById('add-detail-btn');
            if (addBtn) {
                addBtn.addEventListener('click', function() {
                    addDetailRow('New Label', 'New Value');
                });
                logResult('Add Button Setup', true, 'Event listener attached');
            } else {
                logResult('Add Button Setup', false, 'Button not found');
            }
            
            // Test 4: Check for conflicts
            setTimeout(function() {
                const detailRows = document.querySelectorAll('.detail-row').length;
                const tableRows = document.querySelectorAll('#pc-dt-simple tbody tr').length;
                logResult('Conflict Check', true, `${detailRows} detail rows, ${tableRows} table rows - no conflicts detected`);
            }, 1000);
        });
    </script>
</body>
</html>
