# BYU Koreakaja CMS API Documentation

## Overview

The BYU Koreakaja CMS API provides comprehensive endpoints for managing Korean cultural campaign content including events, schedules, FAQs, finalists, winners, merchandise, and special rewards.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

Currently, the API endpoints are publicly accessible. For production use, consider implementing authentication using Laravel Sanctum.

## Response Format

All API responses follow a consistent JSON format:

### Success Response
```json
{
    "success": true,
    "data": { ... },
    "message": "Operation completed successfully"
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description",
    "error": "Detailed error message",
    "errors": { ... } // Validation errors if applicable
}
```

## Endpoints

### 1. Events

Manage campaign events and periods.

#### GET /events
Retrieve all events with optional filtering.

**Query Parameters:**
- `is_active` (boolean): Filter by active status
- `period` (integer): Filter by campaign year
- `search` (string): Search in event names
- `per_page` (integer): Number of results per page (default: 15)

#### POST /events
Create a new event.

**Request Body:**
```json
{
    "event_name": "Koreakaja 2025 Campaign",
    "period": 2025,
    "description": "Annual Korean cultural campaign",
    "is_active": true
}
```

#### GET /events/{id}
Retrieve a specific event with all related data.

#### PUT /events/{id}
Update an existing event.

#### DELETE /events/{id}
Delete an event (only if no related data exists).

### 2. Event Schedules

Manage event schedules and locations.

#### GET /event-schedules
Retrieve all event schedules with optional filtering.

**Query Parameters:**
- `city` (string): Filter by city name
- `is_active` (boolean): Filter by active status
- `date_from` (date): Filter events from this date
- `date_to` (date): Filter events until this date
- `per_page` (integer): Number of results per page

#### POST /event-schedules
Create a new event schedule.

**Request Body:**
```json
{
    "city": "Jakarta",
    "event_date": "2025-07-15",
    "event_end_date": "2025-07-17",
    "location": "Jakarta Convention Center",
    "description": "Korean cultural festival in Jakarta",
    "is_active": true
}
```

### 3. FAQs

Manage frequently asked questions.

#### GET /faqs
Retrieve all FAQs with optional filtering.

**Query Parameters:**
- `is_active` (boolean): Filter by active status
- `campaign_section` (string): Filter by campaign section
- `search` (string): Search in questions and answers
- `per_page` (integer): Number of results per page

#### POST /faqs
Create a new FAQ.

**Request Body:**
```json
{
    "question": "How do I register for the Koreakaja campaign?",
    "answer": "You can register by visiting our official website and filling out the registration form.",
    "campaign_section": "registration",
    "sort_order": 1,
    "is_active": true
}
```

### 4. Finalists

Manage campaign finalists.

#### GET /finalists
Retrieve all finalists with optional filtering.

**Query Parameters:**
- `is_active` (boolean): Filter by active status
- `city` (string): Filter by city
- `badge_status` (boolean): Filter by badge status
- `search` (string): Search in names and usernames
- `per_page` (integer): Number of results per page

#### POST /finalists
Create a new finalist.

**Request Body:**
```json
{
    "rank": 1,
    "name": "John Doe",
    "username": "johndoe_korea",
    "photo": "finalists/photo.jpg",
    "badge_status": true,
    "city": "Jakarta",
    "description": "Top performer in Korean language competition",
    "is_active": true
}
```

### 5. Previous Winners

Manage previous campaign winners.

#### GET /previous-winners
Retrieve all previous winners.

**Query Parameters:**
- `is_active` (boolean): Filter by active status
- `search` (string): Search in event names and winner names
- `per_page` (integer): Number of results per page

#### POST /previous-winners
Create a new previous winner record.

**Request Body:**
```json
{
    "event_name": "Koreakaja 2024 Final",
    "video_url": "https://youtube.com/watch?v=example",
    "winner_name": "Jane Smith",
    "description": "Winner of the 2024 Korean language competition",
    "is_active": true
}
```

### 6. Merchandise Packages

Manage merchandise and data packages.

#### GET /merchandise-packages
Retrieve all merchandise packages.

**Query Parameters:**
- `is_active` (boolean): Filter by active status
- `search` (string): Search in product names and data plans
- `min_price` (decimal): Filter by minimum price
- `max_price` (decimal): Filter by maximum price
- `per_page` (integer): Number of results per page

#### POST /merchandise-packages
Create a new merchandise package.

**Request Body:**
```json
{
    "product_name": "Korea Learning Package",
    "data_plan": "5GB + Korean Learning App",
    "price": 50000,
    "image": "merchandise/package.jpg",
    "duration_days": 30,
    "description": "Complete package for Korean language learning",
    "is_active": true
}
```

### 7. Special Rewards

Manage special rewards with validity periods.

#### GET /special-rewards
Retrieve all special rewards.

**Query Parameters:**
- `is_active` (boolean): Filter by active status
- `validity_status` (string): Filter by validity status (active, expired, expiring_soon)
- `search` (string): Search in reward names and data plans
- `per_page` (integer): Number of results per page

#### POST /special-rewards
Create a new special reward.

**Request Body:**
```json
{
    "image": "rewards/special.jpg",
    "reward_name": "Premium Korean Content Access",
    "data_plan": "Unlimited + Premium Features",
    "validity_period_days": 90,
    "price": 100000,
    "description": "Premium access to all Korean learning content",
    "is_active": true
}
```

## File Upload

For endpoints that support file uploads (finalists, merchandise packages, special rewards), use `multipart/form-data` instead of JSON:

```bash
curl -X POST \
  http://localhost:8000/api/v1/finalists \
  -H 'Content-Type: multipart/form-data' \
  -F 'rank=1' \
  -F 'name=John Doe' \
  -F 'username=johndoe_korea' \
  -F 'photo=@/path/to/photo.jpg' \
  -F 'city=Jakarta' \
  -F 'is_active=true'
```

## Error Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Postman Collection

Import the provided Postman collection (`postman/BYU-Koreakaja-API.postman_collection.json`) to test all endpoints with pre-configured examples.

## Rate Limiting

Currently no rate limiting is implemented. For production use, consider implementing rate limiting to prevent abuse.

## Support

For API support and questions, please contact the development team.
