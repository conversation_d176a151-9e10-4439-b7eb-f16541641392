@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-box me-3"></i>Merchandise Package Details
                            </h2>
                            <p class="text-white mb-0">View package information and pricing</p>
                        </div>
                        <div class="action-buttons">
                            <a href="{{ route('admin.merchandise-packages.edit', $merchandisePackage->id) }}"
                                class="btn btn-light">
                                <i class="fas fa-edit me-2"></i>
                                <span class="d-none d-md-inline">Edit</span>
                            </a>
                            <a href="{{ route('admin.merchandise-packages.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>
                                <span class="d-none d-md-inline">Back to List</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Package Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header animated-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Package Information
                        </h5>
                        <div>
                            @if($merchandisePackage->is_active)
                            <span class="badge bg-success fs-6">Active</span>
                            @else
                            <span class="badge bg-danger fs-6">Inactive</span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-tag me-2 text-primary"></i>Product Name
                            </label>
                            <div class="fs-4 fw-bold">{{ $merchandisePackage->product_name }}</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-wifi me-2 text-info"></i>Data Plan
                                </label>
                                <div class="fs-5 fw-semibold">{{ $merchandisePackage->data_plan }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-dollar-sign me-2 text-success"></i>Price
                                </label>
                                <div class="fs-5 fw-semibold">${{ number_format($merchandisePackage->price, 2) }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-calendar-days me-2 text-warning"></i>Duration
                            </label>
                            <div class="fs-5 fw-semibold">{{ $merchandisePackage->duration_days }} days</div>
                        </div>
                    </div>

                    @if($merchandisePackage->description)
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-align-left me-2 text-secondary"></i>Description
                            </label>
                            <div class="fs-6">{{ $merchandisePackage->description }}</div>
                        </div>
                    </div>
                    @endif

                    @if($merchandisePackage->product_url)
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-link me-2 text-info"></i>Product URL
                            </label>
                            <div class="fs-6">
                                <a href="{{ $merchandisePackage->product_url }}" target="_blank"
                                    class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-external-link-alt me-2"></i>Visit Product Page
                                </a>
                                <div class="text-muted small mt-1">{{ $merchandisePackage->product_url }}</div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-plus-circle me-2 text-primary"></i>Created At
                                </label>
                                <div class="fs-6">{{ $merchandisePackage->created_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-edit me-2 text-warning"></i>Last Updated
                                </label>
                                <div class="fs-6">{{ $merchandisePackage->updated_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            @if($merchandisePackage->image_url)
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-image me-2"></i>Package Image
                    </h5>
                </div>
                <div class="card-body text-center">
                    <img src="{{ $merchandisePackage->image_url }}" alt="{{ $merchandisePackage->product_name }}"
                        class="img-fluid rounded mb-3" style="max-height: 300px; object-fit: cover;">
                    <div class="mt-3">
                        <small class="text-muted">{{ $merchandisePackage->image_url }}</small>
                    </div>
                </div>
            </div>
            @else
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-image me-2"></i>Package Image
                    </h5>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No image available</h6>
                    <p class="text-muted small">No image URL has been provided for this package.</p>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex flex-column flex-md-row justify-content-between gap-3">
                        <a href="{{ route('admin.merchandise-packages.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                        <div class="action-buttons">
                            <a href="{{ route('admin.merchandise-packages.edit', $merchandisePackage->id) }}"
                                class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Edit Package
                            </a>
                            <form action="{{ route('admin.merchandise-packages.destroy', $merchandisePackage->id) }}"
                                method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger"
                                    onclick="return confirm('Are you sure you want to delete this package?')">
                                    <i class="fas fa-trash me-2"></i>Delete Package
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<style>
    .info-item {
        padding: 1rem;
        background: linear-gradient(135deg, rgba(135, 206, 235, 0.1) 0%, rgba(173, 216, 230, 0.1) 100%);
        border-radius: 10px;
        border-left: 4px solid var(--bubblegum-sky);
        transition: all 0.3s ease;
    }

    .info-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
</style>
@endsection