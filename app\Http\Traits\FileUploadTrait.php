<?php

namespace App\Http\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

trait FileUploadTrait
{
    /**
     * Upload a file and return the path
     */
    protected function uploadFile(UploadedFile $file, string $directory = 'uploads'): string
    {
        // Generate unique filename
        $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        
        // Store file in public disk
        $path = $file->storeAs($directory, $filename, 'public');
        
        return $path;
    }

    /**
     * Upload an image with validation and return the path
     */
    protected function uploadImage(UploadedFile $file, string $directory = 'images'): string
    {
        // Validate image
        $this->validateImage($file);
        
        return $this->uploadFile($file, $directory);
    }

    /**
     * Delete a file from storage
     */
    protected function deleteFile(string $path): bool
    {
        if (Storage::disk('public')->exists($path)) {
            return Storage::disk('public')->delete($path);
        }
        
        return false;
    }

    /**
     * Get full URL for a stored file
     */
    protected function getFileUrl(string $path): string
    {
        return asset('storage/' . $path);
    }

    /**
     * Validate image file
     */
    protected function validateImage(UploadedFile $file): void
    {
        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            throw new \InvalidArgumentException('File size must be less than 5MB');
        }

        // Check file type
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $allowedTypes)) {
            throw new \InvalidArgumentException('File must be an image (jpg, jpeg, png, gif, webp)');
        }

        // Check MIME type
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \InvalidArgumentException('Invalid file type');
        }
    }

    /**
     * Handle file upload for a model field
     */
    protected function handleFileUpload(array &$data, string $fieldName, string $directory = 'images'): void
    {
        if (request()->hasFile($fieldName)) {
            $file = request()->file($fieldName);
            
            try {
                $path = $this->uploadImage($file, $directory);
                $data[$fieldName] = $path;
            } catch (\InvalidArgumentException $e) {
                throw new \Illuminate\Validation\ValidationException(
                    validator([], []),
                    [$fieldName => [$e->getMessage()]]
                );
            }
        }
    }

    /**
     * Handle file update (delete old file if exists)
     */
    protected function handleFileUpdate(array &$data, string $fieldName, ?string $oldPath = null, string $directory = 'images'): void
    {
        if (request()->hasFile($fieldName)) {
            // Delete old file if exists
            if ($oldPath) {
                $this->deleteFile($oldPath);
            }
            
            // Upload new file
            $this->handleFileUpload($data, $fieldName, $directory);
        }
    }

    /**
     * Get validation rules for image upload
     */
    protected function getImageValidationRules(): array
    {
        return [
            'image',
            'mimes:jpeg,jpg,png,gif,webp',
            'max:5120' // 5MB in kilobytes
        ];
    }
}
