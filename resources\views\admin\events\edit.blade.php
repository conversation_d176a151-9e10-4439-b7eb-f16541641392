@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <i class="fas fa-edit me-2" style="color: #FF6B6B;"></i>
                                Edit Event: {{ $event->event_name }}
                            </h4>
                            <p class="text-muted mb-0">Update event information</p>
                        </div>
                        <a href="{{ route('admin.events.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Events
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8 col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Event Information</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.events.update', $event->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="event_name" class="form-label">Event Name <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('event_name') is-invalid @enderror"
                                        id="event_name" name="event_name"
                                        value="{{ old('event_name', $event->event_name) }}"
                                        placeholder="Enter event name" required>
                                    @error('event_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="period" class="form-label">Period (Year) <span
                                            class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('period') is-invalid @enderror"
                                        id="period" name="period" value="{{ old('period', $event->period) }}" min="2020"
                                        max="2050" required>
                                    @error('period')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description"
                                name="description" rows="4"
                                placeholder="Enter event description">{{ old('description', $event->description) }}</textarea>
                            @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                    value="1" {{ old('is_active', $event->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label fw-semibold" for="is_active">
                                    <i class="fas fa-toggle-on me-2 text-success"></i>Active Event
                                </label>
                                <div class="form-text">Active events will be visible in the system</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.events.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Event
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Event Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Created:</strong><br>
                        <span class="text-muted">{{ $event->local_created_at->format('M d, Y H:i') }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <span class="text-muted">{{ $event->local_updated_at->format('M d, Y H:i') }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong><br>
                        @if($event->is_active)
                        <span class="badge bg-success">Active</span>
                        @else
                        <span class="badge bg-secondary">Inactive</span>
                        @endif
                    </div>

                    <hr>

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Warning</h6>
                        <p class="mb-0 small">Changing the event period may affect related data. Please ensure all
                            related content is properly managed.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection