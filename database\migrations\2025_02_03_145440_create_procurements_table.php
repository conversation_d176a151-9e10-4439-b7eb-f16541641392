<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateProcurementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('procurements', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->string('product_name')->nullable();
            $table->string('product_category');
            $table->integer('price')->nullable();
            $table->integer('quantity')->nullable();
            $table->integer('requester_user_id')->nullable();
            $table->string('supervisor_approval');
            $table->string('manager_approval');
            $table->string('status')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('procurements');
    }
}
