<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class EventController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Event::query();

            // Filter by active status
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Filter by period
            if ($request->has('period')) {
                $query->where('period', $request->period);
            }

            // Search by event name
            if ($request->has('search')) {
                $query->where('event_name', 'like', '%' . $request->search . '%');
            }

            // Sort by period (newest first by default)
            $query->orderBy('period', 'desc');

            $perPage = $request->get('per_page', 15);
            $events = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $events,
                'message' => 'Events retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve events',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'event_name' => 'required|string|max:255',
                'period' => 'required|integer|min:2020|max:2050',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $event = Event::create($validated);

            return response()->json([
                'success' => true,
                'data' => $event,
                'message' => 'Event created successfully'
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $event = Event::with([
                'eventSchedules' => function($query) {
                    $query->where('is_active', true);
                },
                'faqs' => function($query) {
                    $query->where('is_active', true);
                },
                'finalists' => function($query) {
                    $query->where('is_active', true);
                },
                'previousWinners' => function($query) {
                    $query->where('is_active', true);
                },
                'merchandisePackages' => function($query) {
                    $query->where('is_active', true);
                },
                'specialRewards' => function($query) {
                    $query->where('is_active', true);
                }
            ])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $event,
                'message' => 'Event retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Event not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $event = Event::findOrFail($id);

            $validated = $request->validate([
                'event_name' => 'sometimes|required|string|max:255',
                'period' => 'sometimes|required|integer|min:2020|max:2050',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $event->update($validated);

            return response()->json([
                'success' => true,
                'data' => $event->fresh(),
                'message' => 'Event updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $event = Event::findOrFail($id);
            
            // Check if event has related data
            $hasRelatedData = $event->eventSchedules()->count() > 0 ||
                             $event->faqs()->count() > 0 ||
                             $event->finalists()->count() > 0 ||
                             $event->previousWinners()->count() > 0 ||
                             $event->merchandisePackages()->count() > 0 ||
                             $event->specialRewards()->count() > 0;

            if ($hasRelatedData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete event with related data. Please remove all related content first.'
                ], 400);
            }

            $event->delete();

            return response()->json([
                'success' => true,
                'message' => 'Event deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete event',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
