<?php

namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\WhitelistMsisdn;
use Illuminate\Http\Request;
use DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\StudentDetail;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    public function index()
    {
        $data['courses'] = Course::where([
            'is_active' => true
        ])->limit(6)->with(['category', 'instructor', 'enrolled'])->get();
        $data['instructors'] = User::where([
            'role' => 'instructor',
        ])->count();
        $data['students'] = User::where([
            'role' => 'student',
        ])->count();
        return view('home.index', $data);
    }

    public function signUp()
    {
        return view('home.register');
    }

    public function register(Request $request)
    {
        // dd($request->all());
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'nik' => 'required|string|max:20|unique:student_details,nik',
            'tempat_lahir' => 'required|string|max:255',
            'tanggal_lahir' => 'required|date',
            'alamat_ktp' => 'required|string',
            'agama' => 'required|string',
            'nomor_whatsapp' => ['required', 'string', 'regex:/^(08|62)\d{8,16}$/'],
            'pendidikan_terakhir' => 'required|string|max:255',
            'jurusan_pendidikan' => 'required|string|max:255',
            'instansi' => 'required|string|max:255',
            'nama_instansi' => 'required|string|max:255',
            // 'ktp' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], [
            'name.required' => 'Nama wajib diisi.',
            'name.string' => 'Nama harus berupa string.',
            'name.max' => 'Nama tidak boleh lebih dari :max karakter.',

            'email.required' => 'Email wajib diisi.',
            'email.string' => 'Email harus berupa string.',
            'email.email' => 'Format email tidak valid.',
            'email.max' => 'Email tidak boleh lebih dari :max karakter.',
            'email.unique' => 'Email sudah terdaftar.',

            'password.required' => 'Password wajib diisi.',
            'password.string' => 'Password harus berupa string.',
            'password.min' => 'Password harus terdiri dari minimal :min karakter.',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',

            'nik.required' => 'NIK wajib diisi.',
            'nik.string' => 'NIK harus berupa string.',
            'nik.max' => 'NIK tidak boleh lebih dari :max karakter.',
            'nik.unique' => 'NIK sudah terdaftar.',

            'tempat_lahir.required' => 'Tempat lahir wajib diisi.',
            'tempat_lahir.string' => 'Tempat lahir harus berupa string.',
            'tempat_lahir.max' => 'Tempat lahir tidak boleh lebih dari :max karakter.',

            'tanggal_lahir.required' => 'Tanggal lahir wajib diisi.',
            'tanggal_lahir.date' => 'Format tanggal lahir tidak valid.',

            'alamat_ktp.required' => 'Alamat KTP wajib diisi.',
            'alamat_ktp.string' => 'Alamat KTP harus berupa string.',

            'agama.required' => 'Agama wajib diisi.',
            'agama.string' => 'Agama harus berupa string.',

            'nomor_whatsapp.required' => 'Nomor WhatsApp wajib diisi.',
            'nomor_whatsapp.string' => 'Nomor WhatsApp harus berupa string.',
            'nomor_whatsapp.regex' => 'Nomor WhatsApp tidak valid.',

            'pendidikan_terakhir.required' => 'Pendidikan terakhir wajib diisi.',
            'pendidikan_terakhir.string' => 'Pendidikan terakhir harus berupa string.',
            'pendidikan_terakhir.max' => 'Pendidikan terakhir tidak boleh lebih dari :max karakter.',

            'jurusan_pendidikan.required' => 'Jurusan pendidikan wajib diisi.',
            'jurusan_pendidikan.string' => 'Jurusan pendidikan harus berupa string.',
            'jurusan_pendidikan.max' => 'Jurusan pendidikan tidak boleh lebih dari :max karakter.',

            'instansi.required' => 'Instansi wajib diisi.',
            'instansi.string' => 'Instansi harus berupa string.',
            'instansi.max' => 'Instansi tidak boleh lebih dari :max karakter.',

            'nama_instansi.required' => 'Nama instansi wajib diisi.',
            'nama_instansi.string' => 'Nama instansi harus berupa string.',
            'nama_instansi.max' => 'Nama instansi tidak boleh lebih dari :max karakter.',

            // 'ktp.required' => 'KTP wajib diunggah.',
            // 'ktp.image' => 'File KTP harus berupa gambar.',
            // 'ktp.mimes' => 'KTP harus bertipe jpeg, png, jpg, atau gif.',
            // 'ktp.max' => 'Ukuran file KTP tidak boleh lebih dari :max kilobyte.',
        ]);

        DB::beginTransaction();

        try {
            // Proses penyimpanan data user
            $user = new User();
            $user->name = $request->name;
            $user->role = 'student';
            $user->email = $request->email;
            $user->password = Hash::make($request->password);
            $user->save();

            // Proses penyimpanan data detail siswa
            $studentDetail = new StudentDetail();
            $studentDetail->user_id = $user->id;
            $studentDetail->nik = $request->nik;
            $studentDetail->tempat_lahir = $request->tempat_lahir;
            $studentDetail->tanggal_lahir = $request->tanggal_lahir;
            $studentDetail->alamat_ktp = $request->alamat_ktp;
            $studentDetail->agama = $request->agama;
            $studentDetail->nomor_whatsapp = $request->nomor_whatsapp;
            $studentDetail->pendidikan_terakhir = $request->pendidikan_terakhir;
            $studentDetail->jurusan_pendidikan = $request->jurusan_pendidikan;
            $studentDetail->instansi = $request->instansi;
            $studentDetail->nama_instansi = $request->nama_instansi;


            if ($request->hasFile('ktp')) {
                $ktp = $request->file('ktp');
                $file_name = $user->id . '_' . $request->file('ktp')->getClientOriginalName();
                $ktp->move('uploads/ktp', $file_name);
                $studentDetail->ktp = $file_name;
            }

            $studentDetail->save();

            DB::commit();

            // Redirect atau response sesuai kebutuhan
            return redirect()->route('home.sign-in')->with('success', 'Pendaftaran berhasil. Silakan login.');
        } catch (\Exception $e) {
            DB::rollback();
            dd($e);
            return redirect()->back()->with('error', 'Terjadi kesalahan saat mendaftar. Silakan coba lagi.');
        }

    }
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required',
            'password' => 'required',
        ]);

        $username = $request->get('username');
        $password = $request->get('password');

        $user = User::where('username', $username)->first();
        if ($user && \Hash::check($password, $user->password)) {
            // Check user role and restrict to superadmin/admin only
            if (!in_array($user->role, ['superadmin', 'admin'])) {
                return redirect('/sign-in')->with('error', 'Access denied. Only admin users are allowed.');
            }

            auth()->guard('web')->login($user);
            session(["username" => $username]);

            if(in_array(auth()->user()->role, ['superadmin', 'admin'])){
                return redirect('/admin');
            }
        } else {
            return redirect('/sign-in')->with('error', 'Username atau Password Salah!');
        }

        if (Auth::attempt($credentials, $request->filled('remember'))) {
            $request->session()->regenerate();

            return redirect()->route('student.index')->with('success', 'Berhasil masuk.');
        }

        return back()->withErrors([
            'email' => 'Kredensial yang diberikan tidak cocok dengan catatan kami.',
        ])->onlyInput('email');
    }

    public function signIn()
    {
        return view('home.login');
    }

    public function signOut(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect()->route('home.login')->with('success', 'Anda telah berhasil keluar.');
    }

}
