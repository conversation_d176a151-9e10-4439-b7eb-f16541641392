{"info": {"_postman_id": "koreakaja-cms-api-collection", "name": "Koreakaja CMS API Collection", "description": "API collection for Koreakaja CMS - GET endpoints only for listing and detail views", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "koreakaja-cms"}, "item": [{"name": "Event Schedules", "item": [{"name": "Get All Event Schedules", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/event-schedules?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "event-schedules"], "query": [{"key": "per_page", "value": "15", "description": "Number of results per page"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "city", "value": "", "description": "Filter by city name", "disabled": true}, {"key": "search", "value": "", "description": "Search in city, location, description", "disabled": true}, {"key": "date_from", "value": "", "description": "Filter events from this date (YYYY-MM-DD)", "disabled": true}, {"key": "date_to", "value": "", "description": "Filter events until this date (YYYY-MM-DD)", "disabled": true}]}, "description": "Retrieve all event schedules with optional filtering by city, active status, date range, and search functionality."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/event-schedules?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "event-schedules"], "query": [{"key": "per_page", "value": "15"}, {"key": "is_active", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"current_page\": 1,\n        \"data\": [\n            {\n                \"id\": 1,\n                \"event_id\": 1,\n                \"city\": \"Bekasi\",\n                \"event_date\": \"2025-06-27T00:00:00.000000Z\",\n                \"event_end_date\": \"2025-06-27T00:00:00.000000Z\",\n                \"location\": \"Summarecon\",\n                \"description\": \"Lorem ipsum\",\n                \"is_active\": true,\n                \"created_at\": \"2025-06-27T03:54:01.000000Z\",\n                \"updated_at\": \"2025-06-27T03:54:01.000000Z\",\n                \"deleted_at\": null\n            },\n            {\n                \"id\": 3,\n                \"event_id\": 1,\n                \"city\": \"Jakarta\",\n                \"event_date\": \"2025-07-07T00:00:00.000000Z\",\n                \"event_end_date\": \"2025-07-09T00:00:00.000000Z\",\n                \"location\": \"Monas\",\n                \"description\": \"lorem\",\n                \"is_active\": true,\n                \"created_at\": \"2025-07-03T06:53:55.000000Z\",\n                \"updated_at\": \"2025-07-03T06:53:55.000000Z\",\n                \"deleted_at\": null\n            }\n        ],\n        \"first_page_url\": \"http://localhost?page=1\",\n        \"from\": 1,\n        \"last_page\": 1,\n        \"last_page_url\": \"http://localhost?page=1\",\n        \"links\": [\n            {\"url\": null, \"label\": \"&laquo; Previous\", \"active\": false},\n            {\"url\": \"http://localhost?page=1\", \"label\": \"1\", \"active\": true},\n            {\"url\": null, \"label\": \"Next &raquo;\", \"active\": false}\n        ],\n        \"next_page_url\": null,\n        \"path\": \"http://localhost\",\n        \"per_page\": 15,\n        \"prev_page_url\": null,\n        \"to\": 3,\n        \"total\": 3\n    },\n    \"message\": \"Event schedules retrieved successfully\"\n}"}, {"name": "Error Response - Server Error", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/event-schedules", "host": ["{{base_url}}"], "path": ["api", "v1", "event-schedules"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Failed to retrieve event schedules\",\n    \"error\": \"Database connection error\"\n}"}]}, {"name": "Get Event Schedule Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/event-schedules/{{schedule_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "event-schedules", "{{schedule_id}}"]}, "description": "Retrieve details of a specific event schedule by ID."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/event-schedules/1", "host": ["{{base_url}}"], "path": ["api", "v1", "event-schedules", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"event_id\": 1,\n        \"city\": \"Bekasi\",\n        \"event_date\": \"2025-06-27T00:00:00.000000Z\",\n        \"event_end_date\": \"2025-06-27T00:00:00.000000Z\",\n        \"location\": \"Summarecon\",\n        \"description\": \"Lorem ipsum\",\n        \"is_active\": true,\n        \"created_at\": \"2025-06-27T03:54:01.000000Z\",\n        \"updated_at\": \"2025-06-27T03:54:01.000000Z\",\n        \"deleted_at\": null\n    },\n    \"message\": \"Event schedule retrieved successfully\"\n}"}, {"name": "Error Response - Not Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/event-schedules/999", "host": ["{{base_url}}"], "path": ["api", "v1", "event-schedules", "999"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Event schedule not found\",\n    \"error\": \"No query results for model [App\\\\Models\\\\EventSchedule] 999\"\n}"}]}], "description": "Event schedules management endpoints for retrieving schedule information and locations."}, {"name": "FAQs", "item": [{"name": "Get All FAQs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/faqs?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "faqs"], "query": [{"key": "per_page", "value": "15", "description": "Number of results per page"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "campaign_section", "value": "", "description": "Filter by campaign section", "disabled": true}, {"key": "search", "value": "", "description": "Search in questions and answers", "disabled": true}]}, "description": "Retrieve all FAQs with optional filtering by campaign section, active status, and search functionality."}}, {"name": "Get FAQ Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/faqs/{{faq_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "faqs", "{{faq_id}}"]}, "description": "Retrieve details of a specific FAQ by ID."}}], "description": "Frequently Asked Questions management endpoints for retrieving FAQ information."}, {"name": "Finalists", "item": [{"name": "Get All Finalists", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/finalists?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "finalists"], "query": [{"key": "per_page", "value": "15", "description": "Number of results per page"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "city", "value": "", "description": "Filter by city name", "disabled": true}, {"key": "badge_status", "value": "", "description": "Filter by badge status (true/false)", "disabled": true}, {"key": "search", "value": "", "description": "Search in name and username", "disabled": true}]}, "description": "Retrieve all finalists with optional filtering by city, badge status, active status, and search functionality."}}, {"name": "Get Finalist Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/finalists/{{finalist_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "finalists", "{{finalist_id}}"]}, "description": "Retrieve details of a specific finalist by ID."}}], "description": "Finalists management endpoints for retrieving finalist information and rankings."}, {"name": "Merchandise Packages", "item": [{"name": "Get All Merchandise Packages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/merchandise-packages?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "merchandise-packages"], "query": [{"key": "per_page", "value": "15", "description": "Number of results per page"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "search", "value": "", "description": "Search in product name and data plan", "disabled": true}, {"key": "price_min", "value": "", "description": "Minimum price filter", "disabled": true}, {"key": "price_max", "value": "", "description": "Maximum price filter", "disabled": true}, {"key": "duration_min", "value": "", "description": "Minimum duration in days", "disabled": true}, {"key": "duration_max", "value": "", "description": "Maximum duration in days", "disabled": true}, {"key": "sort_by", "value": "created_at", "description": "Sort by field (price, created_at)", "disabled": true}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)", "disabled": true}]}, "description": "Retrieve all merchandise packages with optional filtering by price range, duration, active status, and search functionality."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/merchandise-packages?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "merchandise-packages"], "query": [{"key": "per_page", "value": "15"}, {"key": "is_active", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"current_page\": 1,\n        \"data\": [\n            {\n                \"id\": 1,\n                \"event_id\": 1,\n                \"product_name\": \"Korean Culture Package\",\n                \"data_plan\": \"10GB + Premium Content\",\n                \"price\": \"50000.00\",\n                \"image\": \"packages/sample.jpg\",\n                \"duration_days\": 30,\n                \"description\": \"Sample merchandise package for testing\",\n                \"product_url\": \"https://example.com/korean-culture-package\",\n                \"is_active\": true,\n                \"created_at\": \"2025-07-03T12:28:24.000000Z\",\n                \"updated_at\": \"2025-07-06T09:04:22.000000Z\",\n                \"deleted_at\": null,\n                \"formatted_price\": \"Rp 50.000\"\n            }\n        ],\n        \"first_page_url\": \"http://localhost?page=1\",\n        \"from\": 1,\n        \"last_page\": 1,\n        \"last_page_url\": \"http://localhost?page=1\",\n        \"links\": [\n            {\"url\": null, \"label\": \"&laquo; Previous\", \"active\": false},\n            {\"url\": \"http://localhost?page=1\", \"label\": \"1\", \"active\": true},\n            {\"url\": null, \"label\": \"Next &raquo;\", \"active\": false}\n        ],\n        \"next_page_url\": null,\n        \"path\": \"http://localhost\",\n        \"per_page\": 15,\n        \"prev_page_url\": null,\n        \"to\": 1,\n        \"total\": 1\n    },\n    \"message\": \"Merchandise packages retrieved successfully\"\n}"}]}, {"name": "Get Merchandise Package Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/merchandise-packages/{{package_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "merchandise-packages", "{{package_id}}"]}, "description": "Retrieve details of a specific merchandise package by ID."}}], "description": "Merchandise packages management endpoints for retrieving product information and pricing."}, {"name": "Special Rewards", "item": [{"name": "Get All Special Rewards", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/special-rewards?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "special-rewards"], "query": [{"key": "per_page", "value": "15", "description": "Number of results per page"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "validity_status", "value": "", "description": "Filter by validity status (active, expired, expiring_soon)", "disabled": true}, {"key": "search", "value": "", "description": "Search in reward name and data plan", "disabled": true}, {"key": "price_min", "value": "", "description": "Minimum price filter", "disabled": true}, {"key": "price_max", "value": "", "description": "Maximum price filter", "disabled": true}, {"key": "validity_min", "value": "", "description": "Minimum validity period in days", "disabled": true}, {"key": "validity_max", "value": "", "description": "Maximum validity period in days", "disabled": true}]}, "description": "Retrieve all special rewards with optional filtering by validity status, price range, validity period, and search functionality."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/special-rewards?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "special-rewards"], "query": [{"key": "per_page", "value": "15"}, {"key": "is_active", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"current_page\": 1,\n        \"data\": [\n            {\n                \"id\": 1,\n                \"event_id\": 1,\n                \"image\": \"rewards/sample.jpg\",\n                \"reward_name\": \"Premium Korean Access\",\n                \"data_plan\": \"Unlimited + Premium Features\",\n                \"validity_period_days\": 90,\n                \"price\": \"100000.00\",\n                \"description\": \"Sample special reward for testing\",\n                \"product_url\": \"https://example.com/premium-korean-access\",\n                \"is_active\": true,\n                \"created_at\": \"2025-07-03T12:28:24.000000Z\",\n                \"updated_at\": \"2025-07-06T09:04:22.000000Z\",\n                \"deleted_at\": null,\n                \"formatted_price\": \"Rp 100.000\"\n            }\n        ],\n        \"first_page_url\": \"http://localhost?page=1\",\n        \"from\": 1,\n        \"last_page\": 1,\n        \"last_page_url\": \"http://localhost?page=1\",\n        \"links\": [\n            {\"url\": null, \"label\": \"&laquo; Previous\", \"active\": false},\n            {\"url\": \"http://localhost?page=1\", \"label\": \"1\", \"active\": true},\n            {\"url\": null, \"label\": \"Next &raquo;\", \"active\": false}\n        ],\n        \"next_page_url\": null,\n        \"path\": \"http://localhost\",\n        \"per_page\": 15,\n        \"prev_page_url\": null,\n        \"to\": 1,\n        \"total\": 1\n    },\n    \"message\": \"Special rewards retrieved successfully\"\n}"}]}, {"name": "Get Special Reward Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/special-rewards/{{reward_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "special-rewards", "{{reward_id}}"]}, "description": "Retrieve details of a specific special reward by ID."}}], "description": "Special rewards management endpoints for retrieving reward information with validity periods."}, {"name": "Previous Winners", "item": [{"name": "Get All Previous Winners", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/previous-winners?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "previous-winners"], "query": [{"key": "per_page", "value": "15", "description": "Number of results per page"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "search", "value": "", "description": "Search in event name and winner name", "disabled": true}]}, "description": "Retrieve all previous winners with optional filtering by active status and search functionality."}}, {"name": "Get Previous Winner Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/previous-winners/{{winner_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "previous-winners", "{{winner_id}}"]}, "description": "Retrieve details of a specific previous winner by ID."}}], "description": "Previous winners management endpoints for retrieving winner information and achievements."}, {"name": "Special Quotas", "item": [{"name": "Get All Special Quotas", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/special-quotas?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "special-quotas"], "query": [{"key": "per_page", "value": "15", "description": "Number of results per page"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "search", "value": "", "description": "Search in quota name, badge, and description", "disabled": true}, {"key": "price_min", "value": "", "description": "Minimum special price filter", "disabled": true}, {"key": "price_max", "value": "", "description": "Maximum special price filter", "disabled": true}, {"key": "volume_min", "value": "", "description": "Minimum quota volume", "disabled": true}, {"key": "volume_max", "value": "", "description": "Maximum quota volume", "disabled": true}, {"key": "validity_min", "value": "", "description": "Minimum validity days", "disabled": true}, {"key": "validity_max", "value": "", "description": "Maximum validity days", "disabled": true}, {"key": "sort_by", "value": "created_at", "description": "Sort by field (special_price, created_at)", "disabled": true}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)", "disabled": true}]}, "description": "Retrieve all special quotas with optional filtering by price range, volume, validity period, active status, and search functionality."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/special-quotas?per_page=15&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "special-quotas"], "query": [{"key": "per_page", "value": "15"}, {"key": "is_active", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"current_page\": 1,\n        \"data\": [\n            {\n                \"id\": 1,\n                \"event_id\": 1,\n                \"quota_name\": \"Premium Data Package\",\n                \"display_badge\": \"BEST VALUE\",\n                \"quota_volume\": 50,\n                \"validity_days\": 30,\n                \"normal_price\": \"150000.00\",\n                \"special_price\": \"99000.00\",\n                \"description\": {\"details\": [{\"label\": \"Kuota Utuh\", \"value\": \"50 GB\"}, {\"label\": \"Masa Aktif\", \"value\": \"30 hari\"}, {\"label\": \"Kecepatan\", \"value\": \"4G/5G\"}, {\"label\": \"<PERSON>aringan\", \"value\": \"Nasional\"}]},\n                \"product_url\": \"https://example.com/premium-data-package\",\n                \"is_active\": true,\n                \"created_at\": \"2025-07-06T09:15:32.000000Z\",\n                \"updated_at\": \"2025-07-06T13:45:18.000000Z\",\n                \"deleted_at\": null,\n                \"description_details\": [{\"label\": \"Kuota Utuh\", \"value\": \"50 GB\"}, {\"label\": \"Masa Aktif\", \"value\": \"30 hari\"}, {\"label\": \"Kecepatan\", \"value\": \"4G/5G\"}, {\"label\": \"Jaringan\", \"value\": \"Nasional\"}],\n                \"formatted_details\": {\"Kuota Utuh\": \"50 GB\", \"Masa Aktif\": \"30 hari\", \"Kecepatan\": \"4G/5G\", \"Jaringan\": \"Nasional\"},\n                \"discount_percentage\": 34,\n                \"savings_amount\": \"51000.00\",\n                \"formatted_normal_price\": \"Rp 150.000\",\n                \"formatted_special_price\": \"Rp 99.000\",\n                \"formatted_savings\": \"Rp 51.000\"\n            }\n        ],\n        \"first_page_url\": \"http://localhost?page=1\",\n        \"from\": 1,\n        \"last_page\": 1,\n        \"last_page_url\": \"http://localhost?page=1\",\n        \"links\": [\n            {\"url\": null, \"label\": \"&laquo; Previous\", \"active\": false},\n            {\"url\": \"http://localhost?page=1\", \"label\": \"1\", \"active\": true},\n            {\"url\": null, \"label\": \"Next &raquo;\", \"active\": false}\n        ],\n        \"next_page_url\": null,\n        \"path\": \"http://localhost\",\n        \"per_page\": 15,\n        \"prev_page_url\": null,\n        \"to\": 1,\n        \"total\": 1\n    },\n    \"message\": \"Special quotas retrieved successfully\"\n}"}]}, {"name": "Get Special Quota Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/special-quotas/{{quota_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "special-quotas", "{{quota_id}}"]}, "description": "Retrieve details of a specific special quota by ID."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/special-quotas/1", "host": ["{{base_url}}"], "path": ["api", "v1", "special-quotas", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 1,\n        \"event_id\": 1,\n        \"quota_name\": \"Premium Data Package\",\n        \"display_badge\": \"BEST VALUE\",\n        \"quota_volume\": 50,\n        \"validity_days\": 30,\n        \"normal_price\": \"150000.00\",\n        \"special_price\": \"99000.00\",\n        \"description\": {\"details\": [{\"label\": \"Kuota Utuh\", \"value\": \"50 GB\"}, {\"label\": \"Masa Aktif\", \"value\": \"30 hari\"}, {\"label\": \"Kecepatan\", \"value\": \"4G/5G\"}, {\"label\": \"Jaringan\", \"value\": \"Nasional\"}]},\n        \"product_url\": \"https://example.com/premium-data-package\",\n        \"is_active\": true,\n        \"created_at\": \"2025-07-06T09:15:32.000000Z\",\n        \"updated_at\": \"2025-07-06T13:45:18.000000Z\",\n        \"deleted_at\": null,\n        \"description_details\": [{\"label\": \"Kuota Utuh\", \"value\": \"50 GB\"}, {\"label\": \"Masa Aktif\", \"value\": \"30 hari\"}, {\"label\": \"Kecepatan\", \"value\": \"4G/5G\"}, {\"label\": \"Jaringan\", \"value\": \"Nasional\"}],\n        \"formatted_details\": {\"Kuota Utuh\": \"50 GB\", \"Masa Aktif\": \"30 hari\", \"Kecepatan\": \"4G/5G\", \"Jaringan\": \"Nasional\"},\n        \"discount_percentage\": 34,\n        \"savings_amount\": \"51000.00\",\n        \"formatted_normal_price\": \"Rp 150.000\",\n        \"formatted_special_price\": \"Rp 99.000\",\n        \"formatted_savings\": \"Rp 51.000\"\n    },\n    \"message\": \"Special quota retrieved successfully\"\n}"}, {"name": "Error Response - Not Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/special-quotas/999", "host": ["{{base_url}}"], "path": ["api", "v1", "special-quotas", "999"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Special quota not found\",\n    \"error\": \"No query results for model [App\\\\Models\\\\SpecialQuota] 999\"\n}"}]}], "description": "Special quotas management endpoints for retrieving quota information with pricing and validity details."}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "schedule_id", "value": "1", "type": "string", "description": "Valid IDs: 1, 2, 3"}, {"key": "faq_id", "value": "1", "type": "string", "description": "Valid IDs: 1, 2"}, {"key": "finalist_id", "value": "1", "type": "string", "description": "Valid IDs: 1"}, {"key": "package_id", "value": "1", "type": "string", "description": "Valid IDs: 1"}, {"key": "reward_id", "value": "1", "type": "string", "description": "Valid IDs: 1"}, {"key": "winner_id", "value": "1", "type": "string", "description": "Valid IDs: 1"}, {"key": "quota_id", "value": "1", "type": "string", "description": "Valid IDs: 1"}]}