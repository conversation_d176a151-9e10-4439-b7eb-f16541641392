@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-trophy me-3"></i>Finalist Details
                            </h2>
                            <p class="text-white mb-0">View finalist information and achievements</p>
                        </div>
                        <div>
                            <a href="{{ route('admin.finalists.edit', $finalist->id) }}" class="btn btn-light me-2">
                                <i class="fas fa-edit me-2"></i>Edit
                            </a>
                            <a href="{{ route('admin.finalists.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Finalist Profile -->
    <div class="row">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Profile
                    </h5>
                </div>
                <div class="card-body text-center">
                    @if($finalist->photo_url)
                    <img src="{{ $finalist->photo_url }}" alt="{{ $finalist->name }}" class="rounded-circle mb-3"
                        style="width: 120px; height: 120px; object-fit: cover;">
                    @else
                    <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center mx-auto mb-3"
                        style="width: 120px; height: 120px;">
                        <i class="fas fa-user fa-3x text-white"></i>
                    </div>
                    @endif

                    <h4 class="fw-bold">{{ $finalist->name }}</h4>
                    <p class="text-muted mb-2">{{'@'}}{{ $finalist->username }}</p>

                    <div class="d-flex justify-content-center gap-2 mb-3">
                        <span class="badge bg-warning fs-6">
                            <i class="fas fa-medal me-1"></i>Rank #{{ $finalist->rank }}
                        </span>
                        @if($finalist->badge_status)
                        <span class="badge bg-success fs-6">
                            <i class="fas fa-award me-1"></i>Badge Awarded
                        </span>
                        @endif
                    </div>

                    <div class="mb-3">
                        @if($finalist->is_active)
                        <span class="badge bg-success fs-6">Active</span>
                        @else
                        <span class="badge bg-danger fs-6">Inactive</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Detailed Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-medal me-2 text-warning"></i>Rank Position
                                </label>
                                <div class="fs-4 fw-bold text-warning">#{{ $finalist->rank }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>City
                                </label>
                                <div class="fs-5 fw-semibold">{{ $finalist->city }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-user me-2 text-success"></i>Full Name
                                </label>
                                <div class="fs-5 fw-semibold">{{ $finalist->name }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-at me-2 text-info"></i>Username
                                </label>
                                <div class="fs-5 fw-semibold">@{{ $finalist->username }}</div>
                            </div>
                        </div>

                        @if($finalist->description)
                        <div class="col-12 mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-align-left me-2 text-secondary"></i>Description
                                </label>
                                <div class="fs-6">{{ $finalist->description }}</div>
                            </div>
                        </div>
                        @endif

                        <div class="col-md-4 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-plus-circle me-2 text-primary"></i>Created At
                                </label>
                                <div class="fs-6">{{ $finalist->created_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-edit me-2 text-warning"></i>Last Updated
                                </label>
                                <div class="fs-6">{{ $finalist->updated_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-award me-2 text-warning"></i>Badge Status
                                </label>
                                <div class="fs-6">
                                    @if($finalist->badge_status)
                                    <span class="badge bg-success">Awarded</span>
                                    @else
                                    <span class="badge bg-secondary">Not Awarded</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.finalists.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                        <div>
                            <a href="{{ route('admin.finalists.edit', $finalist->id) }}" class="btn btn-warning me-2">
                                <i class="fas fa-edit me-2"></i>Edit Finalist
                            </a>
                            <form action="{{ route('admin.finalists.destroy', $finalist->id) }}" method="POST"
                                class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger"
                                    onclick="return confirm('Are you sure you want to delete this finalist?')">
                                    <i class="fas fa-trash me-2"></i>Delete Finalist
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<style>
    .info-item {
        padding: 1rem;
        background: linear-gradient(135deg, rgba(152, 251, 152, 0.1) 0%, rgba(144, 238, 144, 0.1) 100%);
        border-radius: 10px;
        border-left: 4px solid var(--bubblegum-mint);
        transition: all 0.3s ease;
    }

    .info-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
</style>
@endsection