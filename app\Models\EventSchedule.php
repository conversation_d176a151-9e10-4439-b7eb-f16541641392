<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventSchedule extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'event_id',
        'city',
        'event_date',
        'event_end_date',
        'location',
        'description',
        'is_active'
    ];

    protected $casts = [
        'event_date' => 'date',
        'event_end_date' => 'date',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // Helper methods
    public function getDurationInDaysAttribute()
    {
        return $this->event_date->diffInDays($this->event_end_date) + 1;
    }

    public function getFormattedDateRangeAttribute()
    {
        if ($this->event_date->isSameDay($this->event_end_date)) {
            return $this->event_date->format('M d, Y');
        }

        return $this->event_date->format('M d') . ' - ' . $this->event_end_date->format('M d, Y');
    }

    public function getIsOngoingAttribute()
    {
        $today = now()->toDateString();
        return $today >= $this->event_date->toDateString() && $today <= $this->event_end_date->toDateString();
    }

    public function getIsUpcomingAttribute()
    {
        return now()->toDateString() < $this->event_date->toDateString();
    }

    public function getIsPastAttribute()
    {
        return now()->toDateString() > $this->event_end_date->toDateString();
    }
}
