# Koreakaja CMS API Collection

This Postman collection provides comprehensive access to the Koreakaja CMS API endpoints for retrieving data from all modules except Events. The collection includes only GET endpoints for listing and detail views.

## 📋 Collection Overview

The collection includes the following modules:
- **Event Schedules** - Manage event schedules and locations
- **FAQs** - Frequently Asked Questions management
- **Finalists** - Contest finalists and rankings
- **Merchandise Packages** - Product packages and pricing
- **Special Rewards** - Special rewards with validity periods
- **Previous Winners** - Historical winner information

## 🚀 Getting Started

### 1. Import the Collection
1. Open Postman
2. Click "Import" button
3. Select the `Koreakaja_CMS_API_Collection.postman_collection.json` file
4. The collection will be imported with all endpoints and variables

### 2. Configure Environment Variables
The collection uses the following variables that you can customize:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `base_url` | `http://localhost:8000` | Base URL of your API |
| `schedule_id` | `1` | Sample Event Schedule ID |
| `faq_id` | `1` | Sample FAQ ID |
| `finalist_id` | `1` | Sample Finalist ID |
| `package_id` | `1` | Sample Merchandise Package ID |
| `reward_id` | `1` | Sample Special Reward ID |
| `winner_id` | `1` | Sample Previous Winner ID |

### 3. Update Base URL
Before using the collection, update the `base_url` variable to match your environment:
- **Local Development**: `http://localhost:8000`
- **Staging**: `https://staging.koreakaja.com`
- **Production**: `https://api.koreakaja.com`

## 📚 API Endpoints

### Event Schedules
- **GET** `/api/v1/event-schedules` - List all event schedules
- **GET** `/api/v1/event-schedules/{id}` - Get event schedule details

**Query Parameters:**
- `per_page` - Number of results per page (default: 15)
- `is_active` - Filter by active status (true/false)
- `city` - Filter by city name
- `search` - Search in city, location, description
- `date_from` - Filter events from date (YYYY-MM-DD)
- `date_to` - Filter events until date (YYYY-MM-DD)

### FAQs
- **GET** `/api/v1/faqs` - List all FAQs
- **GET** `/api/v1/faqs/{id}` - Get FAQ details

**Query Parameters:**
- `per_page` - Number of results per page (default: 15)
- `is_active` - Filter by active status (true/false)
- `campaign_section` - Filter by campaign section
- `search` - Search in questions and answers

### Finalists
- **GET** `/api/v1/finalists` - List all finalists
- **GET** `/api/v1/finalists/{id}` - Get finalist details

**Query Parameters:**
- `per_page` - Number of results per page (default: 15)
- `is_active` - Filter by active status (true/false)
- `city` - Filter by city name
- `badge_status` - Filter by badge status (true/false)
- `search` - Search in name and username

### Merchandise Packages
- **GET** `/api/v1/merchandise-packages` - List all merchandise packages
- **GET** `/api/v1/merchandise-packages/{id}` - Get package details

**Query Parameters:**
- `per_page` - Number of results per page (default: 15)
- `is_active` - Filter by active status (true/false)
- `search` - Search in product name and data plan
- `price_min` - Minimum price filter
- `price_max` - Maximum price filter
- `duration_min` - Minimum duration in days
- `duration_max` - Maximum duration in days
- `sort_by` - Sort by field (price, created_at)
- `sort_order` - Sort order (asc, desc)

### Special Rewards
- **GET** `/api/v1/special-rewards` - List all special rewards
- **GET** `/api/v1/special-rewards/{id}` - Get reward details

**Query Parameters:**
- `per_page` - Number of results per page (default: 15)
- `is_active` - Filter by active status (true/false)
- `validity_status` - Filter by validity status (active, expired, expiring_soon)
- `search` - Search in reward name and data plan
- `price_min` - Minimum price filter
- `price_max` - Maximum price filter
- `validity_min` - Minimum validity period in days
- `validity_max` - Maximum validity period in days

### Previous Winners
- **GET** `/api/v1/previous-winners` - List all previous winners
- **GET** `/api/v1/previous-winners/{id}` - Get winner details

**Query Parameters:**
- `per_page` - Number of results per page (default: 15)
- `is_active` - Filter by active status (true/false)
- `search` - Search in event name and winner name

## 🔧 Response Format

All API responses follow a consistent JSON format:

### Success Response
```json
{
    "success": true,
    "data": { ... },
    "message": "Operation completed successfully"
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description",
    "error": "Detailed error message"
}
```

## 📝 Usage Examples

### Get All Active Event Schedules
```
GET {{base_url}}/api/v1/event-schedules?is_active=true&per_page=10
```

### Search FAQs
```
GET {{base_url}}/api/v1/faqs?search=registration&is_active=true
```

### Filter Finalists by City
```
GET {{base_url}}/api/v1/finalists?city=Jakarta&is_active=true
```

### Get Merchandise Packages by Price Range
```
GET {{base_url}}/api/v1/merchandise-packages?price_min=50000&price_max=200000
```

### Filter Special Rewards by Validity Status
```
GET {{base_url}}/api/v1/special-rewards?validity_status=active&is_active=true
```

## 🛠️ Testing Tips

1. **Start with List Endpoints**: Test the list endpoints first to get available IDs
2. **Use Filters**: Utilize query parameters to filter results effectively
3. **Check Pagination**: Use `per_page` parameter to control response size
4. **Test Search**: Try different search terms to verify search functionality
5. **Validate Responses**: Ensure all responses follow the expected JSON format

## 📞 Support

For API support or questions about the Koreakaja CMS system, please contact the development team.

---

**Note**: This collection contains only GET endpoints for data retrieval. For create, update, and delete operations, please refer to the admin panel or contact the development team for additional API access.
