<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('special_rewards', function (Blueprint $table) {
            // Check if columns exist before dropping them
            if (Schema::hasColumn('special_rewards', 'valid_from')) {
                $table->dropColumn('valid_from');
            }
            
            if (Schema::hasColumn('special_rewards', 'valid_until')) {
                $table->dropColumn('valid_until');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('special_rewards', function (Blueprint $table) {
            // Re-add the columns if needed for rollback
            $table->date('valid_from')->nullable()->after('validity_period_days');
            $table->date('valid_until')->nullable()->after('valid_from');
        });
    }
};
