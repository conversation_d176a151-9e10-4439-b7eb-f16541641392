<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use Alert;
use Auth;
use App\Models\Procurement;
use Illuminate\Http\Request;

class ProcurementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        
        // Tambahkan validasi untuk memeriksa role admin
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'superadmin') {
                abort(403, 'Akses Ditolak. Selain role Admin tidak diizinkan mengakses halaman ini.');
            }
            return $next($request);
        });
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $perPage = 25;
        $procurement = Procurement::latest()->paginate($perPage);
        $data['procurement'] = $procurement;
        return view('admin.procurement.index', $data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.procurement.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        
        $requestData = $request->all();
        
        Procurement::create($requestData);
        alert()->success('New ' . 'Procurement'. ' Created!' );

        return redirect('admin/procurement');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $procurement = Procurement::findOrFail($id);

        return view('admin.procurement.show', compact('procurement'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $procurement = Procurement::findOrFail($id);
        $data['procurement'] = $procurement;
        return view('admin.procurement.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        
        $requestData = $request->all();
        
        $procurement = Procurement::findOrFail($id);
        alert()->success('Record Updated!' );
        $procurement->update($requestData);

        return redirect('admin/procurement');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        alert()->success('Record Deleted!' );
        Procurement::destroy($id);

        return redirect('admin/procurement');
    }
}
