<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('previous_winners', function (Blueprint $table) {
            $table->string('event_name')->after('id');
            $table->string('video_url')->nullable()->after('event_name');
            $table->string('winner_name')->nullable()->after('video_url');
            $table->date('event_date')->nullable()->after('winner_name');
            $table->text('description')->nullable()->after('event_date');
            $table->boolean('is_active')->default(true)->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('previous_winners', function (Blueprint $table) {
            $table->dropColumn([
                'event_name',
                'video_url',
                'winner_name',
                'event_date',
                'description',
                'is_active'
            ]);
        });
    }
};
