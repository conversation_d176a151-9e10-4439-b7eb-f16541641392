@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-plus-circle me-3"></i>Add New Finalist
                            </h2>
                            <p class="text-white mb-0">Create a new competition finalist</p>
                        </div>
                        <a href="{{ route('admin.finalists.index') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>Finalist Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.finalists.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        {{-- Event ID will be automatically set by the controller using EventFilterTrait --}}

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="rank" class="form-label fw-semibold">
                                    <i class="fas fa-medal me-2 text-warning"></i>Rank *
                                </label>
                                <input type="number" class="form-control @error('rank') is-invalid @enderror" id="rank"
                                    name="rank" value="{{ old('rank') }}" min="1" placeholder="Enter rank position"
                                    required>
                                @error('rank')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="city" class="form-label fw-semibold">
                                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>City *
                                </label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" id="city"
                                    name="city" value="{{ old('city') }}" placeholder="Enter city name" required>
                                @error('city')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="name" class="form-label fw-semibold">
                                    <i class="fas fa-user me-2 text-success"></i>Full Name *
                                </label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name"
                                    name="name" value="{{ old('name') }}" placeholder="Enter full name" required>
                                @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="username" class="form-label fw-semibold">
                                    <i class="fas fa-at me-2 text-info"></i>Username *
                                </label>
                                <input type="text" class="form-control @error('username') is-invalid @enderror"
                                    id="username" name="username" value="{{ old('username') }}"
                                    placeholder="Enter username" required>
                                @error('username')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="photo" class="form-label fw-semibold">
                                <i class="fas fa-image me-2 text-secondary"></i>Upload Image
                            </label>
                            <input type="file" class="form-control @error('photo') is-invalid @enderror" id="photo"
                                name="photo" accept="image/*">
                            @error('photo')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Optional: Upload the finalist's photo (JPEG, PNG, GIF, WebP - Max
                                5MB)</div>
                            <div id="photo-preview" class="mt-2" style="display: none;">
                                <img id="preview-image" src="" alt="Preview" class="img-thumbnail"
                                    style="max-width: 200px; max-height: 200px;">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label fw-semibold">
                                <i class="fas fa-align-left me-2 text-secondary"></i>Description
                            </label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description"
                                name="description" rows="4"
                                placeholder="Enter description or additional details">{{ old('description') }}</textarea>
                            @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="badge_status"
                                        name="badge_status" value="1" {{ old('badge_status') ? 'checked' : '' }}>
                                    <label class="form-check-label fw-semibold" for="badge_status">
                                        <i class="fas fa-medal me-2 text-warning"></i>Badge Status
                                    </label>
                                    <div class="form-text">Award badge to this finalist</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                        value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label fw-semibold" for="is_active">
                                        <i class="fas fa-toggle-on me-2 text-success"></i>Active Status
                                    </label>
                                    <div class="form-text">Enable this finalist to be visible</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.finalists.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Finalist
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on first input
    document.getElementById('rank').focus();

    // Photo upload preview
    const photoInput = document.getElementById('photo');
    const photoPreview = document.getElementById('photo-preview');
    const previewImage = document.getElementById('preview-image');

    photoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB');
                this.value = '';
                photoPreview.style.display = 'none';
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid image file (JPEG, PNG, GIF, WebP)');
                this.value = '';
                photoPreview.style.display = 'none';
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                photoPreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            photoPreview.style.display = 'none';
        }
    });
});
</script>
@endsection