<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use Alert;
use Auth;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        
        // Tambahkan validasi untuk memeriksa role admin
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role != 'superadmin') {
                abort(403, 'Akses <PERSON>lak. Selain role Admin tidak diizinkan mengakses halaman ini.');
            }
            return $next($request);
        });
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $perPage = 25;
        $user = User::latest()->paginate($perPage);
        $data['user'] = $user;
        return view('admin.user.index', $data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.user.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        // Validate input
        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6',
            'role' => 'required|in:superadmin,admin',
            'event_id' => 'nullable|exists:events,id'
        ]);

        // Hash password sebelum disimpan
        $requestData = $request->all();
        $requestData['password'] = bcrypt($request->password);

        // Buat user baru
        User::create($requestData);
        alert()->success('Sukses', 'User baru berhasil dibuat!');

        return redirect('admin/user');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $user = User::findOrFail($id);

        return view('admin.user.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $user = User::findOrFail($id);
        $data['user'] = $user;
        return view('admin.user.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        // Validasi email unik kecuali untuk user yang sedang diupdate
        $request->validate([
            'email' => 'required|email|unique:users,email,'.$id,
        ]);

        $user = User::findOrFail($id);
        $requestData = $request->all();

        // Cek jika password diisi, hash password baru
        if (!empty($requestData['password'])) {
            $requestData['password'] = bcrypt($requestData['password']);
        } else {
            // Jika password tidak diisi, hapus dari array requestData
            unset($requestData['password']);
        }

        $user->update($requestData);
        alert()->success('Sukses', 'Data user berhasil diperbarui!');

        return redirect('admin/user');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        alert()->success('Record Deleted!' );
        User::destroy($id);

        return redirect('admin/user');
    }
}
