<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PreviousWinner;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class PreviousWinnerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = PreviousWinner::query();

            // Filter by active status
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Search in event name and winner name
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('event_name', 'like', '%' . $search . '%')
                      ->orWhere('winner_name', 'like', '%' . $search . '%');
                });
            }

            // Sort by created date (newest first) since event_date column doesn't exist
            $query->orderBy('created_at', 'desc');

            $perPage = $request->get('per_page', 15);
            $previousWinners = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $previousWinners,
                'message' => 'Previous winners retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve previous winners',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'event_name' => 'required|string|max:255',
                'video_url' => 'required|url',
                'winner_name' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $previousWinner = PreviousWinner::create($validated);

            return response()->json([
                'success' => true,
                'data' => $previousWinner,
                'message' => 'Previous winner created successfully'
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create previous winner',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $previousWinner = PreviousWinner::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $previousWinner,
                'message' => 'Previous winner retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Previous winner not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $previousWinner = PreviousWinner::findOrFail($id);

            $validated = $request->validate([
                'event_name' => 'sometimes|required|string|max:255',
                'video_url' => 'sometimes|required|url',
                'winner_name' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $previousWinner->update($validated);

            return response()->json([
                'success' => true,
                'data' => $previousWinner->fresh(),
                'message' => 'Previous winner updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update previous winner',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $previousWinner = PreviousWinner::findOrFail($id);
            $previousWinner->delete();

            return response()->json([
                'success' => true,
                'message' => 'Previous winner deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete previous winner',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
