<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\HasLocalTimestamps;

class Event extends Model
{
    use HasFactory, SoftDeletes, HasLocalTimestamps;

    protected $fillable = [
        'event_name',
        'period',
        'description',
        'is_active'
    ];

    protected $casts = [
        'period' => 'integer',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function eventSchedules()
    {
        return $this->hasMany(EventSchedule::class);
    }

    public function faqs()
    {
        return $this->hasMany(Faq::class);
    }

    public function finalists()
    {
        return $this->hasMany(Finalist::class);
    }

    public function previousWinners()
    {
        return $this->hasMany(PreviousWinner::class);
    }

    public function merchandisePackages()
    {
        return $this->hasMany(MerchandisePackage::class);
    }

    public function specialRewards()
    {
        return $this->hasMany(SpecialReward::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Helper methods
    public function getFormattedPeriodAttribute()
    {
        return $this->period . ' Campaign';
    }
}
