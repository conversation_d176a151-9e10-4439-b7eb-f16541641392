<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\EventFilterTrait;
use App\Models\EventSchedule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;

class EventScheduleController extends Controller
{
    use EventFilterTrait;
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (!in_array(Auth::user()->role, ['superadmin', 'admin'])) {
                abort(403, 'Access Denied. Only Admin roles are allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = EventSchedule::query();

        // Apply event filter
        $query = $this->applyEventFilter($query);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('city', 'like', '%' . $search . '%')
                  ->orWhere('location', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        // Filter by city
        if ($request->has('city') && !empty($request->city)) {
            $query->where('city', $request->city);
        }

        // Filter by active status
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $eventSchedules = $query->orderBy('event_date', 'asc')->paginate(15);
        $cities = EventSchedule::distinct()->pluck('city');

        return view('admin.event-schedules.index', compact('eventSchedules', 'cities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.event-schedules.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if an event is selected
        $redirectResponse = $this->requireActiveEvent();
        if ($redirectResponse) {
            Alert::warning('Warning', 'Please select an event first before creating new content.');
            return $redirectResponse;
        }

        $validated = $request->validate([
            'city' => 'required|string|max:255',
            'event_date' => 'required|date',
            'event_end_date' => 'required|date|after_or_equal:event_date',
            'location' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        // Set event_id for new record
        $this->setEventIdForNewRecord($validated);

        EventSchedule::create($validated);
        Alert::success('Success', 'Event schedule created successfully!');

        return redirect()->route('admin.event-schedules.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $eventSchedule = EventSchedule::findOrFail($id);
        return view('admin.event-schedules.show', compact('eventSchedule'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $eventSchedule = EventSchedule::findOrFail($id);
        return view('admin.event-schedules.edit', compact('eventSchedule'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $eventSchedule = EventSchedule::findOrFail($id);

        $validated = $request->validate([
            'city' => 'required|string|max:255',
            'event_date' => 'required|date',
            'event_end_date' => 'required|date|after_or_equal:event_date',
            'location' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        $eventSchedule->update($validated);
        Alert::success('Success', 'Event schedule updated successfully!');

        return redirect()->route('admin.event-schedules.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $eventSchedule = EventSchedule::findOrFail($id);
            $eventSchedule->delete();
            Alert::success('Success', 'Event schedule deleted successfully!');
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to delete event schedule!');
        }

        return redirect()->route('admin.event-schedules.index');
    }
}
