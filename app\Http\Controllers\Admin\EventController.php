<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\Request;
use Alert;

class EventController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        
        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (auth()->user()->role != 'superadmin') {
                abort(403, 'Access Denied. Only admin role is allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Event::query();

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('event_name', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        // Filter by period
        if ($request->has('period') && !empty($request->period)) {
            $query->where('period', $request->period);
        }

        // Filter by active status
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $events = $query->orderBy('period', 'desc')->paginate(15);
        return view('admin.events.index', compact('events'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.events.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'event_name' => 'required|string|max:255',
            'period' => 'required|integer|min:2020|max:2050',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        Event::create($validated);
        Alert::success('Success', 'Event created successfully!');

        return redirect()->route('admin.events.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $event = Event::with(['eventSchedules', 'faqs', 'finalists', 'previousWinners', 'merchandisePackages', 'specialRewards'])->findOrFail($id);
        return view('admin.events.show', compact('event'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $event = Event::findOrFail($id);
        return view('admin.events.edit', compact('event'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $event = Event::findOrFail($id);

        $validated = $request->validate([
            'event_name' => 'required|string|max:255',
            'period' => 'required|integer|min:2020|max:2050',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        $event->update($validated);
        Alert::success('Success', 'Event updated successfully!');

        return redirect()->route('admin.events.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $event = Event::findOrFail($id);
        
        // Check if event has related data
        $hasRelatedData = $event->eventSchedules()->count() > 0 ||
                         $event->faqs()->count() > 0 ||
                         $event->finalists()->count() > 0 ||
                         $event->previousWinners()->count() > 0 ||
                         $event->merchandisePackages()->count() > 0 ||
                         $event->specialRewards()->count() > 0;

        if ($hasRelatedData) {
            Alert::warning('Warning', 'Cannot delete event with related data. Please remove all related content first.');
            return redirect()->route('admin.events.index');
        }

        $event->delete();
        Alert::success('Success', 'Event deleted successfully!');

        return redirect()->route('admin.events.index');
    }

    /**
     * Set the active event for the current session and user
     */
    public function setActiveEvent(Request $request)
    {
        $eventId = $request->input('event_id');
        $user = auth()->user();

        if ($eventId) {
            $event = Event::findOrFail($eventId);

            // For superadmin users, only update session
            if ($user->role === 'superadmin') {
                session(['active_event_id' => $eventId]);
                Alert::success('Success', "Switched to event: {$event->event_name}");
            }

            // For admin users, update both session and user's event_id
            if ($user->role === 'admin') {
                session(['active_event_id' => $eventId]);
                $user->update(['event_id' => $eventId]);
                Alert::success('Success', "Switched to event: {$event->event_name} and updated your assigned event");
            }
        } else {
            // Clear session for all users
            session()->forget('active_event_id');

            // For admin users, also clear their assigned event_id
            if ($user->role === 'admin') {
                $user->update(['event_id' => null]);
                Alert::info('Info', 'Viewing all events and cleared your assigned event');
            } else {
                Alert::info('Info', 'Viewing all events');
            }
        }

        return redirect()->back();
    }
}
