<!doctype html>
<html lang="en">
@include('admin.partials.head')

<body data-pc-preset="preset-1" data-pc-sidebar-caption="true" data-pc-layout="vertical" data-pc-direction="ltr"
    data-pc-theme_contrast="" data-pc-theme="light">
    @include('admin.partials.preloader')
    @include('admin.partials.navbar')
    @include('admin.partials.header')

    <!-- [Snow Particles Container] -->
    <div class="snow-particles" id="snow-particles"></div>

    <div class="pc-container">
        <div class="pc-content main-content-enhanced">
            @yield('content')
        </div>
    </div>
    @include('admin.partials.footer')
    @include('admin.partials.scripts')

    <!-- [Snow Particles & Gradient CSS] -->
    <style>
        .snow-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 999;
            overflow: hidden;
        }

        .snow-particle {
            position: absolute;
            background: radial-gradient(circle, #ffffff 0%, #f0f8ff 50%, transparent 100%);
            border-radius: 50%;
            opacity: 0.8;
            animation: snowfall linear infinite;
            box-shadow: 0 0 6px rgba(255, 255, 255, 0.6);
        }

        .snow-particle:nth-child(even) {
            background: radial-gradient(circle, #e3f2fd 0%, #bbdefb 50%, transparent 100%);
        }

        @keyframes snowfall {
            0% {
                transform: translateY(-100vh) translateX(0px) rotate(0deg);
                opacity: 0;
            }

            10% {
                opacity: 0.8;
            }

            90% {
                opacity: 0.8;
            }

            100% {
                transform: translateY(100vh) translateX(100px) rotate(360deg);
                opacity: 0;
            }
        }

        .main-content-enhanced {
            background: linear-gradient(135deg,
                    rgba(67, 200, 244, 0.1) 0%,
                    rgba(33, 150, 243, 0.05) 25%,
                    rgba(25, 118, 210, 0.1) 50%,
                    rgba(67, 200, 244, 0.05) 75%,
                    rgba(33, 150, 243, 0.1) 100%);
            min-height: 100vh;
            position: relative;
            z-index: 10;
        }

        .main-content-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                    rgba(67, 200, 244, 0.03) 0%,
                    transparent 50%,
                    rgba(67, 200, 244, 0.03) 100%);
            z-index: -1;
        }
    </style>

    <!-- [Snow Particles JavaScript] -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Snow particles script loaded');
            const snowContainer = document.getElementById('snow-particles');
            console.log('Snow container:', snowContainer);
            const snowCount = 30;

            function createSnowflake() {
                const snowflake = document.createElement('div');
                snowflake.className = 'snow-particle';

                // Random size between 2px and 6px
                const size = Math.random() * 4 + 2;
                snowflake.style.width = size + 'px';
                snowflake.style.height = size + 'px';

                // Random horizontal position
                snowflake.style.left = Math.random() * 100 + '%';

                // Random animation duration between 4s and 10s
                const duration = Math.random() * 6 + 4;
                snowflake.style.animationDuration = duration + 's';

                // Random delay
                const delay = Math.random() * 3;
                snowflake.style.animationDelay = delay + 's';

                snowContainer.appendChild(snowflake);

                // Remove snowflake after animation completes
                setTimeout(() => {
                    if (snowflake.parentNode) {
                        snowflake.parentNode.removeChild(snowflake);
                    }
                }, (duration + delay) * 1000);
            }

            // Create initial snowflakes
            for (let i = 0; i < snowCount; i++) {
                setTimeout(createSnowflake, Math.random() * 3000);
            }

            // Continuously create new snowflakes
            setInterval(createSnowflake, 300);
        });
    </script>
</body>

</html>