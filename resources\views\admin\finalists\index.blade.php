@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" style="background: linear-gradient(135deg, #98FB98 0%, #32CD32 100%); border: none;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-trophy me-3"></i>Finalists Management
                            </h2>
                            <p class="text-white mb-0">Manage competition finalists organized by city</p>
                        </div>
                        <a href="{{ route('admin.finalists.create') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-plus me-2"></i>Add New Finalist
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.finalists.index') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">Search</label>
                                <input type="text" name="search" class="form-control"
                                    placeholder="Search by name, username, or city..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">City</label>
                                <select name="city" class="form-select">
                                    <option value="">All Cities</option>
                                    @foreach($cities as $city)
                                    <option value="{{ $city }}" {{ request('city')==$city ? 'selected' : '' }}>
                                        {{ $city }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Badge Status</label>
                                <select name="badge_status" class="form-select">
                                    <option value="">All</option>
                                    <option value="1" {{ request('badge_status')==='1' ? 'selected' : '' }}>With Badge
                                    </option>
                                    <option value="0" {{ request('badge_status')==='0' ? 'selected' : '' }}>No Badge
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-semibold">Status</label>
                                <select name="is_active" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="1" {{ request('is_active')==='1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ request('is_active')==='0' ? 'selected' : '' }}>Inactive
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filter
                                    </button>
                                    <button type="button" id="clear-filters" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Finalists List
                        <span class="badge bg-primary ms-2">{{ $finalists->total() }} Total</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if($finalists->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Finalist</th>
                                    <th>City</th>
                                    <th>Badge</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($finalists as $finalist)
                                <tr>
                                    <td>
                                        <span class="badge bg-warning fs-6">#{{ $finalist->rank }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($finalist->photo_url)
                                            <img src="{{ $finalist->photo_url }}" alt="{{ $finalist->name }}"
                                                class="rounded-circle me-3"
                                                style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                            <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3"
                                                style="width: 40px; height: 40px;">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $finalist->name }}</div>
                                                <small class="text-muted">{{'@'}}{{ $finalist->username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-semibold">{{ $finalist->city }}</span>
                                    </td>
                                    <td>
                                        @if($finalist->badge_status)
                                        <span class="badge bg-success">
                                            <i class="fas fa-medal me-1"></i>Badge
                                        </span>
                                        @else
                                        <span class="badge bg-secondary">No Badge</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($finalist->is_active)
                                        <span class="badge bg-success">Active</span>
                                        @else
                                        <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.finalists.show', $finalist->id) }}"
                                                class="btn btn-sm btn-info" title="View Finalist Profile">
                                                <i class="fas fa-eye me-1"></i>
                                                <span class="d-none d-md-inline">View</span>
                                            </a>
                                            <a href="{{ route('admin.finalists.edit', $finalist->id) }}"
                                                class="btn btn-sm btn-warning" title="Edit Finalist">
                                                <i class="fas fa-edit me-1"></i>
                                                <span class="d-none d-md-inline">Edit</span>
                                            </a>
                                            <form action="{{ route('admin.finalists.destroy', $finalist->id) }}"
                                                method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger"
                                                    onclick="return confirm('Are you sure you want to delete this finalist?')"
                                                    title="Delete Finalist">
                                                    <i class="fas fa-trash me-1"></i>
                                                    <span class="d-none d-md-inline">Delete</span>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $finalists->withQueryString()->links() }}
                    </div>
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No finalists found</h5>
                        <p class="text-muted">Start by adding your first finalist.</p>
                        <a href="{{ route('admin.finalists.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Finalist
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when filters change
        const filterForm = document.querySelector('form[action*="finalists.index"]');
        const selectFilters = filterForm.querySelectorAll('select[name="city"], select[name="badge_status"], select[name="is_active"]');

        selectFilters.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });

        // Add search delay for better UX
        const searchInput = filterForm.querySelector('input[name="search"]');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterForm.submit();
            }, 500); // 500ms delay
        });

        // Clear filters functionality
        const clearBtn = document.getElementById('clear-filters');
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                filterForm.querySelectorAll('input, select').forEach(input => {
                    if (input.type === 'text' || input.type === 'search') {
                        input.value = '';
                    } else if (input.tagName === 'SELECT') {
                        input.selectedIndex = 0;
                    }
                });
                filterForm.submit();
            });
        }
    });
</script>
@endsection