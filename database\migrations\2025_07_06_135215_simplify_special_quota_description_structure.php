<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Simplify JSON structure to only contain details array
        $specialQuotas = DB::table('special_quotas')->get();

        foreach ($specialQuotas as $quota) {
            $currentDescription = json_decode($quota->description, true);

            // Extract only the details array, ignore content
            $simplifiedDescription = [
                'details' => $currentDescription['details'] ?? []
            ];

            DB::table('special_quotas')
                ->where('id', $quota->id)
                ->update(['description' => json_encode($simplifiedDescription)]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore content field with empty content
        $specialQuotas = DB::table('special_quotas')->get();

        foreach ($specialQuotas as $quota) {
            $currentDescription = json_decode($quota->description, true);

            // Add back content field with empty value
            $restoredDescription = [
                'content' => '',
                'details' => $currentDescription['details'] ?? []
            ];

            DB::table('special_quotas')
                ->where('id', $quota->id)
                ->update(['description' => json_encode($restoredDescription)]);
        }
    }
};
