<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\EventFilterTrait;
use App\Http\Traits\FileUploadTrait;
use App\Models\SpecialQuota;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;

class SpecialQuotaController extends Controller
{
    use EventFilterTrait, FileUploadTrait;

    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (!in_array(Auth::user()->role, ['superadmin', 'admin'])) {
                abort(403, 'Access Denied. Only Admin roles are allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = SpecialQuota::query();

        // Apply event filter
        $query = $this->applyEventFilter($query);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('quota_name', 'like', '%' . $search . '%')
                  ->orWhere('display_badge', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        // Filter by active status
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->boolean('status'));
        }

        $specialQuotas = $query->with('event')->latest()->paginate(10);

        return view('admin.special-quotas.index', compact('specialQuotas'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.special-quotas.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if an event is selected
        $redirectResponse = $this->requireActiveEvent();
        if ($redirectResponse) {
            Alert::warning('Warning', 'Please select an event first before creating new content.');
            return $redirectResponse;
        }

        $validated = $this->validateSpecialQuotaRequest($request);

        // Set event_id for new record
        $this->setEventIdForNewRecord($validated);

        SpecialQuota::create($validated);
        Alert::success('Success', 'Special quota created successfully!');

        return redirect()->route('admin.special-quotas.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $specialQuota = SpecialQuota::with('event')->findOrFail($id);
        return view('admin.special-quotas.show', compact('specialQuota'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $specialQuota = SpecialQuota::findOrFail($id);
        return view('admin.special-quotas.edit', compact('specialQuota'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $specialQuota = SpecialQuota::findOrFail($id);

        $validated = $this->validateSpecialQuotaRequest($request);

        $specialQuota->update($validated);
        Alert::success('Success', 'Special quota updated successfully!');

        return redirect()->route('admin.special-quotas.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $specialQuota = SpecialQuota::findOrFail($id);
        $specialQuota->delete();
        
        Alert::success('Success', 'Special quota deleted successfully!');
        return redirect()->route('admin.special-quotas.index');
    }

    /**
     * Validate special quota request with JSON description handling
     */
    private function validateSpecialQuotaRequest(Request $request)
    {
        $rules = [
            'quota_name' => 'required|string|max:255',
            'display_badge' => 'required|string|max:255',
            'quota_volume' => 'required|integer|min:1',
            'validity_days' => 'required|integer|min:1',
            'normal_price' => 'required|numeric|min:0',
            'special_price' => 'required|numeric|min:0|lt:normal_price',
            'product_url' => 'nullable|url|max:255',
            'is_active' => 'boolean'
        ];

        // Add validation for dynamic details - make it required
        $rules['details'] = 'required|array|min:1';
        $rules['details.*.label'] = 'required|string|max:255';
        $rules['details.*.value'] = 'required|string|max:255';

        $validated = $request->validate($rules);

        // Process the description data into simplified JSON format
        $description = [
            'details' => []
        ];

        // Process details - ensure we have valid data
        if (isset($validated['details']) && is_array($validated['details'])) {
            foreach ($validated['details'] as $detail) {
                if (isset($detail['label']) && isset($detail['value'])) {
                    $label = trim($detail['label']);
                    $value = trim($detail['value']);

                    // Only add non-empty details
                    if (!empty($label) && !empty($value)) {
                        $description['details'][] = [
                            'label' => $label,
                            'value' => $value
                        ];
                    }
                }
            }
        }

        // Final check - ensure at least one detail exists after processing
        if (empty($description['details'])) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                ['details' => ['At least one valid quota detail is required.']]
            );
        }

        // Remove the details from validated array since we're handling it separately
        unset($validated['details']);

        // Set the processed description
        $validated['description'] = $description;

        return $validated;
    }
}
