<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Traits\EventFilterTrait;
use App\Models\EventSchedule;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class EventScheduleController extends Controller
{
    use EventFilterTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = EventSchedule::query();

            // For public API access, no event filtering needed

            // Filter by city if provided
            if ($request->has('city')) {
                $query->where('city', 'like', '%' . $request->city . '%');
            }

            // Filter by active status
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Search functionality
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('city', 'like', '%' . $search . '%')
                      ->orWhere('location', 'like', '%' . $search . '%')
                      ->orWhere('description', 'like', '%' . $search . '%');
                });
            }

            // Filter by date range
            if ($request->has('date_from')) {
                $query->where('event_date', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->where('event_date', '<=', $request->date_to);
            }

            // Sort by event date
            $query->orderBy('event_date', 'asc');

            $perPage = $request->get('per_page', 15);
            $eventSchedules = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $eventSchedules,
                'message' => 'Event schedules retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve event schedules',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'city' => 'required|string|max:255',
                'event_date' => 'required|date',
                'event_end_date' => 'required|date|after_or_equal:event_date',
                'location' => 'required|string|max:255',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $eventSchedule = EventSchedule::create($validated);

            return response()->json([
                'success' => true,
                'data' => $eventSchedule,
                'message' => 'Event schedule created successfully'
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create event schedule',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $eventSchedule = EventSchedule::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $eventSchedule,
                'message' => 'Event schedule retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Event schedule not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $eventSchedule = EventSchedule::findOrFail($id);

            $validated = $request->validate([
                'city' => 'sometimes|required|string|max:255',
                'event_date' => 'sometimes|required|date',
                'event_end_date' => 'sometimes|required|date|after_or_equal:event_date',
                'location' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string',
                'is_active' => 'boolean'
            ]);

            $eventSchedule->update($validated);

            return response()->json([
                'success' => true,
                'data' => $eventSchedule->fresh(),
                'message' => 'Event schedule updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update event schedule',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $eventSchedule = EventSchedule::findOrFail($id);
            $eventSchedule->delete();

            return response()->json([
                'success' => true,
                'message' => 'Event schedule deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete event schedule',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
