<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Finalist extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'event_id',
        'rank',
        'name',
        'username',
        'photo',
        'badge_status',
        'city',
        'description',
        'is_active'
    ];

    protected $casts = [
        'rank' => 'integer',
        'badge_status' => 'boolean',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // Accessors
    public function getPhotoUrlAttribute()
    {
        if ($this->photo && !str_starts_with($this->photo, 'http')) {
            return asset('storage/' . $this->photo);
        }
        return $this->photo;
    }

    public function getFullPhotoUrlAttribute()
    {
        return $this->photo_url;
    }
}
