<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\EventFilterTrait;
use App\Http\Traits\FileUploadTrait;
use App\Models\SpecialReward;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;

class SpecialRewardController extends Controller
{
    use EventFilterTrait, FileUploadTrait;
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (!in_array(Auth::user()->role, ['superadmin', 'admin'])) {
                abort(403, 'Access Denied. Only Admin roles are allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = SpecialReward::query();

        // Apply event filter
        $query = $this->applyEventFilter($query);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('reward_name', 'like', '%' . $search . '%')
                  ->orWhere('data_plan', 'like', '%' . $search . '%');
            });
        }

        // Filter by active status
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by price range
        if ($request->has('price_min') && !empty($request->price_min)) {
            $query->where('price', '>=', $request->price_min);
        }

        if ($request->has('price_max') && !empty($request->price_max)) {
            $query->where('price', '<=', $request->price_max);
        }

        $specialRewards = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.special-rewards.index', compact('specialRewards'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.special-rewards.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if an event is selected
        $redirectResponse = $this->requireActiveEvent();
        if ($redirectResponse) {
            Alert::warning('Warning', 'Please select an event first before creating new content.');
            return $redirectResponse;
        }

        $validated = $request->validate([
            'image' => array_merge(['nullable'], $this->getImageValidationRules()),
            'reward_name' => 'required|string|max:255',
            'data_plan' => 'required|string|max:255',
            'validity_period_days' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'product_url' => 'nullable|string|max:255|url',
            'is_active' => 'boolean'
        ]);

        // Handle file upload
        $this->handleFileUpload($validated, 'image', 'rewards');

        // Set event_id for new record
        $this->setEventIdForNewRecord($validated);

        SpecialReward::create($validated);
        Alert::success('Success', 'Special reward created successfully!');

        return redirect()->route('admin.special-rewards.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $specialReward = SpecialReward::findOrFail($id);
        return view('admin.special-rewards.show', compact('specialReward'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $specialReward = SpecialReward::findOrFail($id);
        return view('admin.special-rewards.edit', compact('specialReward'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $specialReward = SpecialReward::findOrFail($id);

        $validated = $request->validate([
            'image' => array_merge(['nullable'], $this->getImageValidationRules()),
            'reward_name' => 'required|string|max:255',
            'data_plan' => 'required|string|max:255',
            'validity_period_days' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'product_url' => 'nullable|string|max:255|url',
            'is_active' => 'boolean'
        ]);

        // Handle file update
        $this->handleFileUpdate($validated, 'image', $specialReward->image, 'special-rewards');

        $specialReward->update($validated);
        Alert::success('Success', 'Special reward updated successfully!');

        return redirect()->route('admin.special-rewards.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $specialReward = SpecialReward::findOrFail($id);
            $specialReward->delete();
            Alert::success('Success', 'Special reward deleted successfully!');
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to delete special reward!');
        }

        return redirect()->route('admin.special-rewards.index');
    }
}
