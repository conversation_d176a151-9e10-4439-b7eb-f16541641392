<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SpecialQuota extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'event_id',
        'quota_name',
        'display_badge',
        'quota_volume',
        'validity_days',
        'normal_price',
        'special_price',
        'description',
        'product_url',
        'is_active'
    ];

    protected $casts = [
        'quota_volume' => 'integer',
        'validity_days' => 'integer',
        'normal_price' => 'decimal:2',
        'special_price' => 'decimal:2',
        'description' => 'array',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // Accessors
    public function getDiscountPercentageAttribute()
    {
        if ($this->normal_price > 0) {
            return round((($this->normal_price - $this->special_price) / $this->normal_price) * 100, 2);
        }
        return 0;
    }

    public function getSavingsAmountAttribute()
    {
        return $this->normal_price - $this->special_price;
    }

    public function getFormattedNormalPriceAttribute()
    {
        return 'Rp ' . number_format($this->normal_price, 0, ',', '.');
    }

    public function getFormattedSpecialPriceAttribute()
    {
        return 'Rp ' . number_format($this->special_price, 0, ',', '.');
    }

    public function getFormattedSavingsAttribute()
    {
        return 'Rp ' . number_format($this->savings_amount, 0, ',', '.');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByEvent($query, $eventId)
    {
        return $query->where('event_id', $eventId);
    }

    // Accessor methods for JSON description field

    /**
     * Get the details array from description JSON
     */
    public function getDescriptionDetailsAttribute()
    {
        $description = $this->description;

        // Ensure we have a valid array structure
        if (!is_array($description)) {
            return [];
        }

        // Return the details array, ensuring it's always an array
        $details = $description['details'] ?? [];
        return is_array($details) ? $details : [];
    }

    /**
     * Set description with details only
     */
    public function setDescriptionData($details = [])
    {
        // Ensure details is an array
        if (!is_array($details)) {
            $details = [];
        }

        // Clean and validate details
        $cleanDetails = [];
        foreach ($details as $detail) {
            if (is_array($detail) && isset($detail['label']) && isset($detail['value'])) {
                $cleanDetails[] = [
                    'label' => trim($detail['label']),
                    'value' => trim($detail['value'])
                ];
            }
        }

        $this->description = [
            'details' => $cleanDetails
        ];
    }

    /**
     * Get formatted description details as key-value pairs
     */
    public function getFormattedDetailsAttribute()
    {
        $details = $this->description_details;
        $formatted = [];

        foreach ($details as $detail) {
            if (isset($detail['label']) && isset($detail['value'])) {
                $formatted[$detail['label']] = $detail['value'];
            }
        }

        return $formatted;
    }

    /**
     * Mutator to handle description data when setting via array
     */
    public function setDescriptionAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['description'] = json_encode($value);
        } elseif (is_string($value)) {
            // If it's already JSON string, validate and store
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->attributes['description'] = $value;
            } else {
                // If not valid JSON, create proper structure
                $this->attributes['description'] = json_encode(['details' => []]);
            }
        } else {
            $this->attributes['description'] = json_encode(['details' => []]);
        }
    }

    /**
     * Add a detail to the description
     */
    public function addDetail($label, $value)
    {
        $description = $this->description ?? [];
        $details = $description['details'] ?? [];

        $details[] = [
            'label' => $label,
            'value' => $value
        ];

        $description['details'] = $details;
        $this->description = $description;
    }

    /**
     * Remove a detail by index
     */
    public function removeDetail($index)
    {
        $description = $this->description ?? [];
        $details = $description['details'] ?? [];

        if (isset($details[$index])) {
            unset($details[$index]);
            $description['details'] = array_values($details); // Re-index array
            $this->description = $description;
        }
    }

    /**
     * Update a specific detail
     */
    public function updateDetail($index, $label, $value)
    {
        $description = $this->description ?? [];
        $details = $description['details'] ?? [];

        if (isset($details[$index])) {
            $details[$index] = [
                'label' => $label,
                'value' => $value
            ];
            $description['details'] = $details;
            $this->description = $description;
        }
    }

    /**
     * Validate description JSON structure
     */
    public static function validateDescriptionStructure($description)
    {
        if (!is_array($description)) {
            return false;
        }

        // Check if details exists and is array
        if (!isset($description['details']) || !is_array($description['details'])) {
            return false;
        }

        // Validate each detail has label and value
        foreach ($description['details'] as $detail) {
            if (!is_array($detail) || !isset($detail['label']) || !isset($detail['value'])) {
                return false;
            }
        }

        return true;
    }
}
