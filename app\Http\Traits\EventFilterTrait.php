<?php

namespace App\Http\Traits;

use App\Models\Event;

trait EventFilterTrait
{
    /**
     * Get the currently active event ID from session
     */
    protected function getActiveEventId()
    {
        return session('active_event_id');
    }

    /**
     * Apply event filter to a query based on user permissions
     */
    protected function applyEventFilter($query)
    {
        $user = auth()->user();

        // For superadmin users, use session-based filtering (can view all or specific event)
        if ($user->role === 'superadmin') {
            $activeEventId = $this->getActiveEventId();
            if ($activeEventId) {
                return $query->where('event_id', $activeEventId);
            }
            return $query; // Show all events
        }

        // For admin users, only show their assigned event or latest event
        if ($user->role === 'admin') {
            $eventId = $this->determineEventIdForUser();
            if ($eventId) {
                return $query->where('event_id', $eventId);
            }
        }

        return $query;
    }

    /**
     * Get the active event model
     */
    protected function getActiveEvent()
    {
        $activeEventId = $this->getActiveEventId();
        
        if ($activeEventId) {
            return Event::find($activeEventId);
        }
        
        return null;
    }

    /**
     * Set the event_id for new records based on active event or user's assigned event
     */
    protected function setEventIdForNewRecord(array &$data)
    {
        $eventId = $this->determineEventIdForUser();

        if ($eventId) {
            $data['event_id'] = $eventId;
        }

        return $data;
    }

    /**
     * Determine the appropriate event ID for the current user
     */
    protected function determineEventIdForUser()
    {
        $user = auth()->user();

        // For superadmin users, use session-based active event or fallback to latest active event
        if ($user->role === 'superadmin') {
            $sessionEventId = $this->getActiveEventId();
            if ($sessionEventId) {
                return $sessionEventId;
            }

            // Fallback to the most recently created active event if no session event
            $latestEvent = Event::where('is_active', true)
                ->orderBy('created_at', 'desc')
                ->first();

            return $latestEvent ? $latestEvent->id : null;
        }

        // For admin users, use their assigned event_id or most recent event
        if ($user->role === 'admin') {
            // If user has an assigned event, use it
            if ($user->event_id) {
                return $user->event_id;
            }

            // Otherwise, use the most recently created active event
            $latestEvent = Event::where('is_active', true)
                ->orderBy('created_at', 'desc')
                ->first();

            return $latestEvent ? $latestEvent->id : null;
        }

        return null;
    }

    /**
     * Check if user has access to an event (required for creating new records)
     */
    protected function requireActiveEvent()
    {
        $eventId = $this->determineEventIdForUser();

        if (!$eventId) {
            $user = auth()->user();

            // Check if there are any active events at all
            $hasActiveEvents = Event::where('is_active', true)->exists();

            if (!$hasActiveEvents) {
                return redirect()->back()->with('error', 'No active events found. Please create an active event first.');
            }

            if ($user->role === 'superadmin') {
                return redirect()->back()->with('error', 'Please select an event first before creating new content. Go to Events page to select an active event.');
            } else {
                return redirect()->back()->with('error', 'No event assigned to your account. Please contact an administrator to assign you to an event.');
            }
        }

        return null;
    }

    /**
     * Get all active events for dropdowns
     */
    protected function getActiveEvents()
    {
        return Event::where('is_active', true)->orderBy('period', 'desc')->get();
    }

    /**
     * Share event data with views
     */
    protected function shareEventData()
    {
        $activeEvent = $this->getActiveEvent();
        $activeEvents = $this->getActiveEvents();
        
        view()->share([
            'activeEvent' => $activeEvent,
            'activeEvents' => $activeEvents,
            'hasActiveEvent' => !is_null($activeEvent)
        ]);
    }
}
