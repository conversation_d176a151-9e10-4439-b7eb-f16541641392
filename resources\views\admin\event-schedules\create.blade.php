@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" style="background: linear-gradient(135deg, #FFB6C1 0%, #DDA0DD 100%); border: none;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-plus-circle me-3"></i>Add New Event Schedule
                            </h2>
                            <p class="text-white mb-0">Create a new Koreakaja event schedule</p>
                        </div>
                        <a href="{{ route('admin.event-schedules.index') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>Event Schedule Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.event-schedules.store') }}" method="POST">
                        @csrf
                        {{-- Event ID will be automatically set by the controller using EventFilterTrait --}}

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="city" class="form-label fw-semibold">
                                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>City *
                                </label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" id="city"
                                    name="city" value="{{ old('city') }}" placeholder="Enter city name" required>
                                @error('city')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="event_date" class="form-label fw-semibold">
                                    <i class="fas fa-calendar me-2 text-success"></i>Event Start Date *
                                </label>
                                <input type="date" class="form-control @error('event_date') is-invalid @enderror"
                                    id="event_date" name="event_date" value="{{ old('event_date') }}" required>
                                @error('event_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="event_end_date" class="form-label fw-semibold">
                                    <i class="fas fa-calendar me-2 text-warning"></i>Event End Date *
                                </label>
                                <input type="date" class="form-control @error('event_end_date') is-invalid @enderror"
                                    id="event_end_date" name="event_end_date" value="{{ old('event_end_date') }}"
                                    required>
                                @error('event_end_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="location" class="form-label fw-semibold">
                                <i class="fas fa-building me-2 text-info"></i>Location *
                            </label>
                            <input type="text" class="form-control @error('location') is-invalid @enderror"
                                id="location" name="location" value="{{ old('location') }}"
                                placeholder="Enter venue or location name" required>
                            @error('location')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>



                        <div class="mb-4">
                            <label for="description" class="form-label fw-semibold">
                                <i class="fas fa-align-left me-2 text-secondary"></i>Description
                            </label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description"
                                name="description" rows="4"
                                placeholder="Enter event description or additional details">{{ old('description') }}</textarea>
                            @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                    value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label fw-semibold" for="is_active">
                                    <i class="fas fa-toggle-on me-2 text-success"></i>Active Status
                                </label>
                                <div class="form-text">Enable this event schedule to be visible and active</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.event-schedules.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Event Schedule
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on first input
    document.getElementById('city').focus();

    // Validate end date is after or equal to start date
    const startDate = document.getElementById('event_date');
    const endDate = document.getElementById('event_end_date');

    function validateDates() {
        if (startDate.value && endDate.value) {
            if (endDate.value < startDate.value) {
                endDate.setCustomValidity('End date must be after or equal to start date');
            } else {
                endDate.setCustomValidity('');
            }
        }
    }

    startDate.addEventListener('change', validateDates);
    endDate.addEventListener('change', validateDates);
});
</script>
@endsection