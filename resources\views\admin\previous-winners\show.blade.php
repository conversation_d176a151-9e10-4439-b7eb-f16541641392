@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-crown me-3"></i>Previous Winner Details
                            </h2>
                            <p class="text-white mb-0">View winner record and video content</p>
                        </div>
                        <div>
                            <a href="{{ route('admin.previous-winners.edit', $previousWinner->id) }}" class="btn btn-light me-2">
                                <i class="fas fa-edit me-2"></i>Edit
                            </a>
                            <a href="{{ route('admin.previous-winners.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Winner Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header animated-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Winner Information
                        </h5>
                        <div>
                            @if($previousWinner->is_active)
                                <span class="badge bg-success fs-6">Active</span>
                            @else
                                <span class="badge bg-danger fs-6">Inactive</span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-calendar-alt me-2 text-primary"></i>Event Name
                            </label>
                            <div class="fs-4 fw-bold">{{ $previousWinner->event_name }}</div>
                        </div>
                    </div>

                    @if($previousWinner->winner_name)
                        <div class="mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-user me-2 text-success"></i>Winner Name
                                </label>
                                <div class="fs-5 fw-semibold">{{ $previousWinner->winner_name }}</div>
                            </div>
                        </div>
                    @endif

                    @if($previousWinner->event_date)
                        <div class="mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-calendar me-2 text-info"></i>Event Date
                                </label>
                                <div class="fs-5 fw-semibold">{{ $previousWinner->event_date->format('F d, Y') }}</div>
                                <small class="text-muted">{{ $previousWinner->event_date->format('l') }}</small>
                            </div>
                        </div>
                    @endif

                    @if($previousWinner->description)
                        <div class="mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-align-left me-2 text-secondary"></i>Description
                                </label>
                                <div class="fs-6">{{ $previousWinner->description }}</div>
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-plus-circle me-2 text-primary"></i>Created At
                                </label>
                                <div class="fs-6">{{ $previousWinner->created_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-edit me-2 text-warning"></i>Last Updated
                                </label>
                                <div class="fs-6">{{ $previousWinner->updated_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            @if($previousWinner->video_url)
                <div class="card">
                    <div class="card-header animated-header">
                        <h5 class="mb-0">
                            <i class="fas fa-video me-2"></i>Video Content
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-play-circle fa-4x text-primary mb-3"></i>
                            <div class="fs-6 text-muted mb-3">Click to watch the winner's video</div>
                        </div>
                        <a href="{{ $previousWinner->video_url }}" target="_blank" class="btn btn-primary btn-lg">
                            <i class="fas fa-external-link-alt me-2"></i>Watch Video
                        </a>
                        <div class="mt-3">
                            <small class="text-muted">{{ $previousWinner->video_url }}</small>
                        </div>
                    </div>
                </div>
            @else
                <div class="card">
                    <div class="card-header animated-header">
                        <h5 class="mb-0">
                            <i class="fas fa-video me-2"></i>Video Content
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <i class="fas fa-video-slash fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No video available</h6>
                        <p class="text-muted small">No video URL has been provided for this winner.</p>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.previous-winners.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                        <div>
                            <a href="{{ route('admin.previous-winners.edit', $previousWinner->id) }}" class="btn btn-warning me-2">
                                <i class="fas fa-edit me-2"></i>Edit Winner
                            </a>
                            <form action="{{ route('admin.previous-winners.destroy', $previousWinner->id) }}" 
                                  method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger" 
                                        onclick="return confirm('Are you sure you want to delete this winner record?')">
                                    <i class="fas fa-trash me-2"></i>Delete Winner
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<style>
.info-item {
    padding: 1rem;
    background: linear-gradient(135deg, rgba(135, 206, 235, 0.1) 0%, rgba(173, 216, 230, 0.1) 100%);
    border-radius: 10px;
    border-left: 4px solid var(--bubblegum-sky);
    transition: all 0.3s ease;
}

.info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
@endsection
