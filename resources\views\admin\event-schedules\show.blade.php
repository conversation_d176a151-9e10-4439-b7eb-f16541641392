@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" style="background: linear-gradient(135deg, #FFB6C1 0%, #DDA0DD 100%); border: none;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-calendar-alt me-3"></i>Event Schedule Details
                            </h2>
                            <p class="text-white mb-0">View event schedule information</p>
                        </div>
                        <div class="action-buttons">
                            <a href="{{ route('admin.event-schedules.edit', $eventSchedule->id) }}"
                                class="btn btn-light">
                                <i class="fas fa-edit me-2"></i>
                                <span class="d-none d-md-inline">Edit</span>
                            </a>
                            <a href="{{ route('admin.event-schedules.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>
                                <span class="d-none d-md-inline">Back to List</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Details -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Event Information
                        </h5>
                        @if($eventSchedule->is_active)
                        <span class="badge bg-success fs-6">Active</span>
                        @else
                        <span class="badge bg-danger fs-6">Inactive</span>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>City
                                </label>
                                <div class="fs-5 fw-semibold">{{ $eventSchedule->city }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-calendar me-2 text-success"></i>Event Date Range
                                </label>
                                <div class="fs-5 fw-semibold">{{ $eventSchedule->formatted_date_range }}</div>
                                <small class="text-muted">{{ $eventSchedule->duration_in_days }} day{{
                                    $eventSchedule->duration_in_days > 1 ? 's' : '' }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-building me-2 text-info"></i>Location
                            </label>
                            <div class="fs-5 fw-semibold">{{ $eventSchedule->location }}</div>
                        </div>
                    </div>



                    @if($eventSchedule->description)
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-align-left me-2 text-secondary"></i>Description
                            </label>
                            <div class="fs-6">{{ $eventSchedule->description }}</div>
                        </div>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-plus-circle me-2 text-primary"></i>Created At
                                </label>
                                <div class="fs-6">{{ $eventSchedule->created_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-edit me-2 text-warning"></i>Last Updated
                                </label>
                                <div class="fs-6">{{ $eventSchedule->updated_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex flex-column flex-md-row justify-content-between gap-3">
                        <a href="{{ route('admin.event-schedules.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                        <div class="action-buttons">
                            <a href="{{ route('admin.event-schedules.edit', $eventSchedule->id) }}"
                                class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Edit Event
                            </a>
                            <form action="{{ route('admin.event-schedules.destroy', $eventSchedule->id) }}"
                                method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger"
                                    onclick="return confirm('Are you sure you want to delete this event schedule?')">
                                    <i class="fas fa-trash me-2"></i>Delete Event
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<style>
    .info-item {
        padding: 1rem;
        background: linear-gradient(135deg, rgba(255, 182, 193, 0.1) 0%, rgba(221, 160, 221, 0.1) 100%);
        border-radius: 10px;
        border-left: 4px solid var(--bubblegum-pink);
        transition: all 0.3s ease;
    }

    .info-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
</style>
@endsection