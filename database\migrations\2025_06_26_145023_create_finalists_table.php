<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('finalists', function (Blueprint $table) {
            $table->id();
            $table->integer('rank');
            $table->string('name');
            $table->string('username');
            $table->string('photo_url')->nullable();
            $table->boolean('badge_status')->default(false);
            $table->string('city');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('finalists');
    }
};
