<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SpecialQuota;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SpecialQuotaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = SpecialQuota::query();

            // For public API access, no event filtering needed

            // Filter by active status
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Search in quota name, display badge, and description details
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('quota_name', 'like', '%' . $search . '%')
                      ->orWhere('display_badge', 'like', '%' . $search . '%')
                      ->orWhereRaw("JSON_SEARCH(JSON_EXTRACT(description, '$.details[*].label'), 'one', ?) IS NOT NULL", ['%' . $search . '%'])
                      ->orWhereRaw("JSON_SEARCH(JSON_EXTRACT(description, '$.details[*].value'), 'one', ?) IS NOT NULL", ['%' . $search . '%']);
                });
            }

            // Filter by price range
            if ($request->has('price_min')) {
                $query->where('special_price', '>=', $request->price_min);
            }

            if ($request->has('price_max')) {
                $query->where('special_price', '<=', $request->price_max);
            }

            // Filter by quota volume range
            if ($request->has('volume_min')) {
                $query->where('quota_volume', '>=', $request->volume_min);
            }

            if ($request->has('volume_max')) {
                $query->where('quota_volume', '<=', $request->volume_max);
            }

            // Filter by validity days range
            if ($request->has('validity_min')) {
                $query->where('validity_days', '>=', $request->validity_min);
            }

            if ($request->has('validity_max')) {
                $query->where('validity_days', '<=', $request->validity_max);
            }

            // Sort by special price or created_at
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            $perPage = $request->get('per_page', 15);
            $specialQuotas = $query->paginate($perPage);

            // Transform the data to include formatted description fields
            $specialQuotas->getCollection()->transform(function ($quota) {
                return $this->transformSpecialQuota($quota);
            });

            return response()->json([
                'success' => true,
                'data' => $specialQuotas,
                'message' => 'Special quotas retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve special quotas',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $specialQuota = SpecialQuota::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $this->transformSpecialQuota($specialQuota),
                'message' => 'Special quota retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Special quota not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Transform special quota data for API response
     */
    private function transformSpecialQuota($quota)
    {
        $data = $quota->toArray();

        // Add formatted description fields for enhanced API
        $data['description_details'] = $quota->description_details;
        $data['formatted_details'] = $quota->formatted_details;

        // Add computed fields
        $data['discount_percentage'] = $quota->discount_percentage;
        $data['savings_amount'] = $quota->special_price ?
            number_format($quota->normal_price - $quota->special_price, 2) : '0.00';
        $data['formatted_normal_price'] = 'Rp ' . number_format($quota->normal_price, 0, ',', '.');
        $data['formatted_special_price'] = 'Rp ' . number_format($quota->special_price, 0, ',', '.');
        $data['formatted_savings'] = 'Rp ' . number_format($quota->normal_price - $quota->special_price, 0, ',', '.');

        return $data;
    }
}
