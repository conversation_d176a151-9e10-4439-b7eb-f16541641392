# Koreakaja CMS API Fixes and Postman Collection Guide

## 🔧 Issues Fixed

### 1. Authentication Errors in API Controllers

**Problem**: API endpoints were trying to access `auth()->user()->role` which returned null for unauthenticated requests.

**Solution**: Removed authentication dependencies from public API endpoints:

-   `EventScheduleController`: Removed `applyEventFilter()` call
-   `FaqController`: Removed `applyEventFilter()` call
-   `MerchandisePackageController`: Removed `applyEventFilter()` call

**Files Modified**:

-   `app/Http/Controllers/Api/EventScheduleController.php`
-   `app/Http/Controllers/Api/FaqController.php`
-   `app/Http/Controllers/Api/MerchandisePackageController.php`

### 2. Missing Sample Data

**Problem**: Detail endpoints were failing because no records existed with ID 1.

**Solution**: Created sample data for all modules:

-   Finalist ID: 1 (<PERSON> from Seoul)
-   Merchandise Package ID: 1 (Korean Culture Package)
-   Special Reward ID: 1 (Premium Korean Access)
-   Previous Winner ID: 1 (<PERSON> 2023)

## 📋 API Status Summary

| Module               | List Endpoint | Detail Endpoint | Status   |
| -------------------- | ------------- | --------------- | -------- |
| Event Schedules      | ✅ Working    | ✅ Working      | Fixed    |
| FAQs                 | ✅ Working    | ✅ Working      | Fixed    |
| Finalists            | ✅ Working    | ✅ Working      | Fixed    |
| Merchandise Packages | ✅ Working    | ✅ Working      | Enhanced |
| Special Rewards      | ✅ Working    | ✅ Working      | Enhanced |
| Special Quotas       | ✅ Working    | ✅ Working      | New      |
| Previous Winners     | ✅ Working    | ✅ Working      | Fixed    |

## 📁 Postman Collection

### Collection File

`Koreakaja_CMS_API_Collection.postman_collection.json`

### How to Import

1. Open Postman
2. Click "Import" button
3. Select the JSON file
4. Collection will be imported with all endpoints and variables

### Collection Structure

```
Koreakaja CMS API Collection/
├── Event Schedules/
│   ├── Get All Event Schedules
│   └── Get Event Schedule Details
├── FAQs/
│   ├── Get All FAQs
│   └── Get FAQ Details
├── Finalists/
│   ├── Get All Finalists
│   └── Get Finalist Details
├── Merchandise Packages/
│   ├── Get All Merchandise Packages
│   └── Get Merchandise Package Details
├── Special Rewards/
│   ├── Get All Special Rewards
│   └── Get Special Reward Details
├── Special Quotas/
│   ├── Get All Special Quotas
│   └── Get Special Quota Details
└── Previous Winners/
    ├── Get All Previous Winners
    └── Get Previous Winner Details
```

### Environment Variables

The collection includes these variables:

-   `base_url`: http://localhost:8000 (change as needed)
-   `schedule_id`: 1 (Valid IDs: 1, 2, 3)
-   `faq_id`: 1 (Valid IDs: 1, 2)
-   `finalist_id`: 1 (Valid IDs: 1)
-   `package_id`: 1 (Valid IDs: 1)
-   `reward_id`: 1 (Valid IDs: 1)
-   `quota_id`: 1 (Valid IDs: 1)
-   `winner_id`: 1 (Valid IDs: 1)

### Query Parameters Available

#### Event Schedules

-   `per_page`: Number of results per page
-   `is_active`: Filter by active status
-   `city`: Filter by city name
-   `search`: Search in city, location, description
-   `date_from`: Filter events from this date (YYYY-MM-DD)
-   `date_to`: Filter events until this date (YYYY-MM-DD)

#### FAQs

-   `per_page`: Number of results per page
-   `is_active`: Filter by active status
-   `campaign_section`: Filter by campaign section
-   `search`: Search in questions and answers

#### Finalists

-   `per_page`: Number of results per page
-   `is_active`: Filter by active status
-   `city`: Filter by city name
-   `badge_status`: Filter by badge status (true/false)
-   `search`: Search in name and username

#### Merchandise Packages

-   `per_page`: Number of results per page
-   `is_active`: Filter by active status
-   `search`: Search in product name and data plan
-   `price_min`: Minimum price filter
-   `price_max`: Maximum price filter
-   `duration_min`: Minimum duration in days
-   `duration_max`: Maximum duration in days
-   `sort_by`: Sort by field (price, created_at)
-   `sort_order`: Sort order (asc, desc)

#### Special Rewards

-   `per_page`: Number of results per page
-   `is_active`: Filter by active status
-   `validity_status`: Filter by validity status (active, expired, expiring_soon)
-   `search`: Search in reward name and data plan
-   `price_min`: Minimum price filter
-   `price_max`: Maximum price filter
-   `validity_min`: Minimum validity period in days
-   `validity_max`: Maximum validity period in days

#### Special Quotas

-   `per_page`: Number of results per page
-   `is_active`: Filter by active status
-   `search`: Search in quota name, badge, and description
-   `price_min`: Minimum special price filter
-   `price_max`: Maximum special price filter
-   `volume_min`: Minimum quota volume
-   `volume_max`: Maximum quota volume
-   `validity_min`: Minimum validity days
-   `validity_max`: Maximum validity days
-   `sort_by`: Sort by field (special_price, created_at)
-   `sort_order`: Sort order (asc, desc)

#### Previous Winners

-   `per_page`: Number of results per page
-   `is_active`: Filter by active status
-   `search`: Search in event name and winner name

## 🚀 Usage Examples

### Basic List Request

```
GET {{base_url}}/api/v1/event-schedules?per_page=15&is_active=true
```

### Filtered Search Request

```
GET {{base_url}}/api/v1/faqs?search=korea&campaign_section=general
```

### Detail Request

```
GET {{base_url}}/api/v1/finalists/{{finalist_id}}
```

### Special Quota with Filters

```
GET {{base_url}}/api/v1/special-quotas?price_max=100000&volume_min=30&sort_by=special_price&sort_order=asc
```

## 📝 Response Format

All endpoints return consistent JSON responses:

### Success Response

```json
{
    "success": true,
    "data": { ... },
    "message": "Operation completed successfully"
}
```

### Error Response

```json
{
    "success": false,
    "message": "Error description",
    "error": "Detailed error message"
}
```

## ✅ Testing Verification

All endpoints have been tested and confirmed working:

-   ✅ No authentication errors
-   ✅ All list endpoints return data
-   ✅ All detail endpoints work with valid IDs
-   ✅ Query parameters function correctly
-   ✅ Consistent response format

The APIs are now ready for public consumption without authentication requirements!
