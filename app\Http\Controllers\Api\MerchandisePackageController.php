<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Traits\EventFilterTrait;
use App\Models\MerchandisePackage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class MerchandisePackageController extends Controller
{
    use EventFilterTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = MerchandisePackage::query();

            // For public API access, no event filtering needed

            // Filter by active status
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Search in product name and data plan
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('product_name', 'like', '%' . $search . '%')
                      ->orWhere('data_plan', 'like', '%' . $search . '%');
                });
            }

            // Filter by price range
            if ($request->has('price_min')) {
                $query->where('price', '>=', $request->price_min);
            }

            if ($request->has('price_max')) {
                $query->where('price', '<=', $request->price_max);
            }

            // Filter by duration range
            if ($request->has('duration_min')) {
                $query->where('duration_days', '>=', $request->duration_min);
            }

            if ($request->has('duration_max')) {
                $query->where('duration_days', '<=', $request->duration_max);
            }

            // Sort by price or created_at
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            $perPage = $request->get('per_page', 15);
            $merchandisePackages = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $merchandisePackages,
                'message' => 'Merchandise packages retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve merchandise packages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'product_name' => 'required|string|max:255',
                'data_plan' => 'required|string|max:255',
                'price' => 'required|numeric|min:0',
                'image' => 'nullable|string',
                'duration_days' => 'required|integer|min:1',
                'description' => 'nullable|string',
                'product_url' => 'nullable|string|max:255|url',
                'is_active' => 'boolean'
            ]);

            $merchandisePackage = MerchandisePackage::create($validated);

            return response()->json([
                'success' => true,
                'data' => $merchandisePackage,
                'message' => 'Merchandise package created successfully'
            ], 201);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create merchandise package',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $merchandisePackage = MerchandisePackage::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $merchandisePackage,
                'message' => 'Merchandise package retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Merchandise package not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $merchandisePackage = MerchandisePackage::findOrFail($id);

            $validated = $request->validate([
                'product_name' => 'sometimes|required|string|max:255',
                'data_plan' => 'sometimes|required|string|max:255',
                'price' => 'sometimes|required|numeric|min:0',
                'image' => 'nullable|string',
                'duration_days' => 'sometimes|required|integer|min:1',
                'description' => 'nullable|string',
                'product_url' => 'nullable|string|max:255|url',
                'is_active' => 'boolean'
            ]);

            $merchandisePackage->update($validated);

            return response()->json([
                'success' => true,
                'data' => $merchandisePackage->fresh(),
                'message' => 'Merchandise package updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update merchandise package',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $merchandisePackage = MerchandisePackage::findOrFail($id);
            $merchandisePackage->delete();

            return response()->json([
                'success' => true,
                'message' => 'Merchandise package deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete merchandise package',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
