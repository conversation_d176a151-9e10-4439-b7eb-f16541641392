<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Home\HomeController;
use App\Http\Controllers\Employee\EmployeeController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('auth.login');
});
Route::get('/auth-user', [App\Http\Controllers\Auth\AuthController::class, 'loginPage'])->name('auth.page');
Route::post('/auth-user', [App\Http\Controllers\Auth\AuthController::class, 'loginUser'])->name('auth.submit');


Auth::routes();
Route::get('/sign-out', [AuthController::class, 'logout'])->name('sign-out');

// Admin routes
Route::get('/admin', [AdminController::class, 'index']);
Route::resource('/admin/user', UserController::class);

// Koreakaja CMS Admin routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::resource('events', \App\Http\Controllers\Admin\EventController::class);
    Route::post('events/set-active', [\App\Http\Controllers\Admin\EventController::class, 'setActiveEvent'])->name('events.set-active');
    Route::resource('event-schedules', \App\Http\Controllers\Admin\EventScheduleController::class);
    Route::resource('faqs', \App\Http\Controllers\Admin\FaqController::class);
    Route::resource('finalists', \App\Http\Controllers\Admin\FinalistController::class);
    Route::resource('previous-winners', \App\Http\Controllers\Admin\PreviousWinnerController::class);
    Route::resource('merchandise-packages', \App\Http\Controllers\Admin\MerchandisePackageController::class);
    Route::resource('special-rewards', \App\Http\Controllers\Admin\SpecialRewardController::class);
    Route::resource('special-quotas', \App\Http\Controllers\Admin\SpecialQuotaController::class);
});



