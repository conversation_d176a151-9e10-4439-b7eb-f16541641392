/* Bubblegum Theme - Korea Kaja <PERSON> */

/* Custom Font */
@font-face {
    font-family: "Ohno Softie";
    src: url("../../vendor/dashboard/assets/fonts/OhnoSoftie.otf")
        format("opentype");
    font-weight: 400;
    font-style: normal;
}

:root {
    /* Bubblegum Color Palette */
    --bubblegum-pink: #ffb6c1;
    --bubblegum-purple: #dda0dd;
    --bubblegum-mint: #98fb98;
    --bubblegum-sky: #87ceeb;
    --bubblegum-lavender: #e6e6fa;
    --bubblegum-peach: #ffcccb;
    --bubblegum-cream: #fff8dc;

    /* Darker variants for text and borders */
    --bubblegum-pink-dark: #ff69b4;
    --bubblegum-purple-dark: #ba55d3;
    --bubblegum-mint-dark: #32cd32;
    --bubblegum-sky-dark: #4682b4;

    /* Neutral colors */
    --bubblegum-white: #ffffff;
    --bubblegum-light-gray: #f8f9fa;
    --bubblegum-gray: #6c757d;
    --bubblegum-dark: #343a40;
}

/* Global Styles */
body {
    background: linear-gradient(
        135deg,
        var(--bubblegum-lavender) 0%,
        var(--bubblegum-cream) 100%
    );
    font-family: "Ohno Softie", "Inter", sans-serif;
    color: var(--bubblegum-dark);
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: "Ohno Softie", "Inter", sans-serif;
    font-weight: 600;
}

/* Login Page Styles */
.auth-main {
    background: linear-gradient(
        135deg,
        var(--bubblegum-pink) 0%,
        var(--bubblegum-purple) 50%,
        var(--bubblegum-sky) 100%
    );
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-wrapper .card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-wrapper .card-body {
    padding: 3rem;
}

.auth-wrapper .form-control {
    border: 2px solid var(--bubblegum-lavender);
    border-radius: 15px;
    padding: 12px 20px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.auth-wrapper .form-control:focus {
    border-color: var(--bubblegum-pink-dark);
    box-shadow: 0 0 0 0.2rem rgba(255, 182, 193, 0.25);
    background: var(--bubblegum-white);
}

.auth-wrapper .btn-primary {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink) 0%,
        var(--bubblegum-purple) 100%
    );
    border: none;
    border-radius: 15px;
    padding: 12px 30px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.auth-wrapper .btn-primary:hover {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink-dark) 0%,
        var(--bubblegum-purple-dark) 100%
    );
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Admin Dashboard Styles */
.pc-container {
    background: var(--bubblegum-light-gray);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.pc-sidebar {
    background: linear-gradient(
        180deg,
        var(--bubblegum-white) 0%,
        var(--bubblegum-lavender) 100%
    );
    border-right: 1px solid var(--bubblegum-lavender);
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    overflow-y: auto;
    z-index: 1000;
}

.pc-navbar {
    background: var(--bubblegum-white);
    border-bottom: 2px solid var(--bubblegum-lavender);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Sidebar Navigation Active States */
.pc-navbar .pc-item .pc-link.active,
.pc-navbar .pc-item .pc-link:hover {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink) 0%,
        var(--bubblegum-purple) 100%
    ) !important;
    color: var(--bubblegum-white) !important;
    border-radius: 10px !important;
    margin: 2px 8px !important;
    transform: translateX(5px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.3);
}

.pc-navbar .pc-item .pc-link.active .pc-micon,
.pc-navbar .pc-item .pc-link:hover .pc-micon {
    color: var(--bubblegum-white) !important;
}

.pc-navbar .pc-item .pc-link {
    transition: all 0.3s ease;
    border-radius: 10px;
    margin: 2px 8px;
    padding: 12px 16px;
}

/* Fix sidebar width and positioning */
.pc-sidebar {
    width: 280px !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    overflow-y: auto !important;
    z-index: 1000 !important;
}

/* Content area adjustment for fixed sidebar */
/* .pc-content,
.content-inner {
    margin-left: 280px !important;
    min-height: 100vh;
    padding: 20px;
    width: calc(100% - 280px) !important;
} */

/* Fix main container */
.pc-container {
    display: flex !important;
    min-height: 100vh !important;
}

/* Mobile responsive */
@media (max-width: 991px) {
    .pc-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .pc-sidebar.pc-sidebar-show {
        transform: translateX(0);
    }

    .pc-content,
    .content-inner {
        margin-left: 0 !important;
        width: 100% !important;
    }
}

/* Card Styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    background: var(--bubblegum-white);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(
        45deg,
        var(--bubblegum-mint) 0%,
        var(--bubblegum-sky) 100%
    );
    border: none;
    border-radius: 15px 15px 0 0;
    color: var(--bubblegum-white);
    font-weight: 600;
    padding: 1.5rem;
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink) 0%,
        var(--bubblegum-purple) 100%
    );
    border: none;
    border-radius: 10px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink-dark) 0%,
        var(--bubblegum-purple-dark) 100%
    );
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(
        45deg,
        var(--bubblegum-mint) 0%,
        var(--bubblegum-mint-dark) 100%
    );
    border: none;
    border-radius: 10px;
}

.btn-info {
    background: linear-gradient(
        45deg,
        var(--bubblegum-sky) 0%,
        var(--bubblegum-sky-dark) 100%
    );
    border: none;
    border-radius: 10px;
}

.btn-warning {
    background: linear-gradient(45deg, var(--bubblegum-peach) 0%, #ffa07a 100%);
    border: none;
    border-radius: 10px;
}

.btn-danger {
    background: linear-gradient(45deg, #ff6b6b 0%, #ff4757 100%);
    border: none;
    border-radius: 10px;
}

/* Form Styles */
.form-control {
    border: 2px solid var(--bubblegum-lavender);
    border-radius: 10px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--bubblegum-pink);
    box-shadow: 0 0 0 0.2rem rgba(255, 182, 193, 0.25);
}

.form-select {
    border: 2px solid var(--bubblegum-lavender);
    border-radius: 10px;
    padding: 10px 15px;
}

.form-select:focus {
    border-color: var(--bubblegum-pink);
    box-shadow: 0 0 0 0.2rem rgba(255, 182, 193, 0.25);
}

/* Table Styles */
.table {
    background: var(--bubblegum-white);
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(
        45deg,
        var(--bubblegum-lavender) 0%,
        var(--bubblegum-mint) 100%
    );
    border: none;
    color: var(--bubblegum-dark);
    font-weight: 600;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: var(--bubblegum-lavender);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    border-color: var(--bubblegum-lavender);
}

/* Badge Styles */
.badge {
    border-radius: 20px;
    padding: 8px 15px;
    font-weight: 500;
}

.badge.bg-success {
    background: linear-gradient(
        45deg,
        var(--bubblegum-mint) 0%,
        var(--bubblegum-mint-dark) 100%
    ) !important;
}

.badge.bg-danger {
    background: linear-gradient(45deg, #ff6b6b 0%, #ff4757 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(
        45deg,
        var(--bubblegum-peach) 0%,
        #ffa07a 100%
    ) !important;
}

.badge.bg-info {
    background: linear-gradient(
        45deg,
        var(--bubblegum-sky) 0%,
        var(--bubblegum-sky-dark) 100%
    ) !important;
}

/* Pagination Styles */
.pagination .page-link {
    border: 2px solid var(--bubblegum-lavender);
    color: var(--bubblegum-purple-dark);
    border-radius: 10px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: var(--bubblegum-pink);
    border-color: var(--bubblegum-pink);
    color: var(--bubblegum-white);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink) 0%,
        var(--bubblegum-purple) 100%
    );
    border-color: var(--bubblegum-pink);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 15px;
    padding: 1rem 1.5rem;
}

.alert-success {
    background: linear-gradient(
        45deg,
        var(--bubblegum-mint) 0%,
        rgba(152, 251, 152, 0.3) 100%
    );
    color: var(--bubblegum-mint-dark);
}

.alert-danger {
    background: linear-gradient(
        45deg,
        rgba(255, 107, 107, 0.3) 0%,
        rgba(255, 71, 87, 0.3) 100%
    );
    color: #ff4757;
}

.alert-warning {
    background: linear-gradient(
        45deg,
        var(--bubblegum-peach) 0%,
        rgba(255, 160, 122, 0.3) 100%
    );
    color: #ffa07a;
}

.alert-info {
    background: linear-gradient(
        45deg,
        var(--bubblegum-sky) 0%,
        rgba(135, 206, 235, 0.3) 100%
    );
    color: var(--bubblegum-sky-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-wrapper .card-body {
        padding: 2rem 1.5rem;
    }

    .card {
        margin: 10px;
    }

    .table-responsive {
        border-radius: 10px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Enhanced Gradient Animation System */
.gradient-animated {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink),
        var(--bubblegum-purple),
        var(--bubblegum-mint),
        var(--bubblegum-sky),
        var(--bubblegum-pink)
    );
    background-size: 500% 500%;
    animation: gradientShift 12s ease infinite;
}

.gradient-animated-header {
    background: linear-gradient(
        135deg,
        var(--bubblegum-pink),
        var(--bubblegum-purple),
        var(--bubblegum-sky),
        var(--bubblegum-mint),
        var(--bubblegum-lavender),
        var(--bubblegum-pink)
    );
    background-size: 400% 400%;
    animation: gradientFlow 10s ease infinite;
}

.gradient-animated-card {
    background: linear-gradient(
        45deg,
        var(--bubblegum-lavender),
        var(--bubblegum-cream),
        var(--bubblegum-peach),
        var(--bubblegum-mint),
        var(--bubblegum-lavender)
    );
    background-size: 300% 300%;
    animation: gentleGradient 15s ease infinite;
}

/* New Enhanced Animation Classes */
.gradient-animated-intense {
    background: linear-gradient(
        90deg,
        var(--bubblegum-pink),
        var(--bubblegum-purple),
        var(--bubblegum-sky),
        var(--bubblegum-mint),
        var(--bubblegum-peach),
        var(--bubblegum-lavender),
        var(--bubblegum-pink)
    );
    background-size: 600% 600%;
    animation: intenseGradient 8s ease infinite;
}

.gradient-animated-footer {
    background: linear-gradient(
        180deg,
        var(--bubblegum-lavender),
        var(--bubblegum-purple),
        var(--bubblegum-pink),
        var(--bubblegum-sky),
        var(--bubblegum-mint),
        var(--bubblegum-lavender)
    );
    background-size: 100% 500%;
    animation: footerGradient 20s ease infinite;
}

.gradient-animated-pulse {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink),
        var(--bubblegum-purple),
        var(--bubblegum-mint),
        var(--bubblegum-sky)
    );
    background-size: 400% 400%;
    animation: pulseGradient 6s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    20% {
        background-position: 100% 0%;
        transform: scale(1.02);
    }
    40% {
        background-position: 100% 100%;
        transform: scale(1);
    }
    60% {
        background-position: 0% 100%;
        transform: scale(1.01);
    }
    80% {
        background-position: 0% 0%;
        transform: scale(1);
    }
    100% {
        background-position: 0% 50%;
        transform: scale(1);
    }
}

@keyframes gradientFlow {
    0% {
        background-position: 0% 0%;
        filter: hue-rotate(0deg);
    }
    25% {
        background-position: 100% 0%;
        filter: hue-rotate(90deg);
    }
    50% {
        background-position: 100% 100%;
        filter: hue-rotate(180deg);
    }
    75% {
        background-position: 0% 100%;
        filter: hue-rotate(270deg);
    }
    100% {
        background-position: 0% 0%;
        filter: hue-rotate(360deg);
    }
}

@keyframes gentleGradient {
    0%,
    100% {
        background-position: 0% 50%;
        opacity: 0.9;
    }
    33% {
        background-position: 100% 0%;
        opacity: 1;
    }
    66% {
        background-position: 100% 100%;
        opacity: 0.95;
    }
}

@keyframes intenseGradient {
    0% {
        background-position: 0% 0%;
        transform: rotate(0deg);
    }
    25% {
        background-position: 100% 0%;
        transform: rotate(1deg);
    }
    50% {
        background-position: 100% 100%;
        transform: rotate(0deg);
    }
    75% {
        background-position: 0% 100%;
        transform: rotate(-1deg);
    }
    100% {
        background-position: 0% 0%;
        transform: rotate(0deg);
    }
}

@keyframes footerGradient {
    0%,
    100% {
        background-position: 0% 0%;
    }
    20% {
        background-position: 0% 25%;
    }
    40% {
        background-position: 0% 50%;
    }
    60% {
        background-position: 0% 75%;
    }
    80% {
        background-position: 0% 100%;
    }
}

@keyframes pulseGradient {
    0%,
    100% {
        background-position: 0% 50%;
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        background-position: 100% 50%;
        transform: scale(1.05);
        opacity: 1;
    }
}

/* Enhanced Card Headers with Animation */
.card-header.animated-header {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink),
        var(--bubblegum-purple),
        var(--bubblegum-mint),
        var(--bubblegum-sky)
    ) !important;
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    color: var(--bubblegum-white);
    font-weight: 600;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
}

/* Page Header Animation */
.page-header-animated {
    background: linear-gradient(
        135deg,
        var(--bubblegum-pink),
        var(--bubblegum-purple),
        var(--bubblegum-sky),
        var(--bubblegum-mint)
    ) !important;
    background-size: 300% 300%;
    animation: gradientFlow 6s ease infinite;
}

/* Sidebar Gradient Animation */
.pc-sidebar {
    background: linear-gradient(
        180deg,
        var(--bubblegum-white) 0%,
        var(--bubblegum-lavender) 50%,
        var(--bubblegum-cream) 100%
    ) !important;
    background-size: 100% 200%;
    animation: verticalGradient 12s ease infinite;
}

@keyframes verticalGradient {
    0%,
    100% {
        background-position: 0% 0%;
    }
    50% {
        background-position: 0% 100%;
    }
}

/* Enhanced Footer Animation Styles */
.footer-enhanced {
    position: relative;
    overflow: hidden;
    padding: 1.5rem 0;
    border-top: 2px solid rgba(255, 255, 255, 0.1);
}

.footer-enhanced::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.15),
        transparent
    );
    animation: footerShimmer 4s ease infinite;
    z-index: 1;
}

.footer-enhanced .footer-wrapper {
    position: relative;
    z-index: 2;
}

/* Legacy footer animation for backward compatibility */
.footer-animated {
    background: linear-gradient(
        180deg,
        var(--bubblegum-lavender),
        var(--bubblegum-purple),
        var(--bubblegum-pink),
        var(--bubblegum-sky),
        var(--bubblegum-mint)
    ) !important;
    background-size: 100% 400%;
    animation: footerGradient 25s ease infinite;
    position: relative;
    overflow: hidden;
}

@keyframes footerShimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Enhanced Body Background */
body {
    background: linear-gradient(
        135deg,
        var(--bubblegum-cream) 0%,
        var(--bubblegum-lavender) 25%,
        var(--bubblegum-peach) 50%,
        var(--bubblegum-mint) 75%,
        var(--bubblegum-sky) 100%
    );
    background-size: 400% 400%;
    animation: bodyGradient 30s ease infinite;
    min-height: 100vh;
}

@keyframes bodyGradient {
    0%,
    100% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 0%;
    }
    50% {
        background-position: 100% 100%;
    }
    75% {
        background-position: 0% 100%;
    }
}

/* Enhanced Action Buttons Styling */
.btn-group .btn {
    border-radius: 8px !important;
    margin: 0 2px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.btn-group .btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s ease;
}

.btn-group .btn:hover::before {
    left: 100%;
}

/* Bubblegum Theme Button Colors */
.btn-info {
    background: linear-gradient(
        45deg,
        var(--bubblegum-sky),
        var(--bubblegum-mint)
    );
    border-color: var(--bubblegum-sky);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(
        45deg,
        var(--bubblegum-mint),
        var(--bubblegum-sky)
    );
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(135, 206, 235, 0.3);
}

.btn-warning {
    background: linear-gradient(45deg, var(--bubblegum-peach), #ffa726);
    border-color: var(--bubblegum-peach);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(45deg, #ffa726, var(--bubblegum-peach));
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 160, 122, 0.3);
}

.btn-danger {
    background: linear-gradient(45deg, #ff6b6b, #ff5252);
    border-color: #ff6b6b;
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(45deg, #ff5252, #ff6b6b);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
}

.btn-primary {
    background: linear-gradient(
        45deg,
        var(--bubblegum-purple),
        var(--bubblegum-pink)
    );
    border-color: var(--bubblegum-purple);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(
        45deg,
        var(--bubblegum-pink),
        var(--bubblegum-purple)
    );
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(221, 160, 221, 0.3);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #5a6268, #6c757d);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
}

/* Responsive Button Groups */
@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin: 2px 0;
        width: 100%;
        border-radius: 8px !important;
    }

    .table-responsive .btn-group {
        min-width: 120px;
    }
}

/* Action Button Container */
.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
}

.action-buttons .btn {
    min-width: 80px;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Enhanced Button Animations */
.btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn:active {
    transform: scale(0.98);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--bubblegum-purple), 0.3);
}
