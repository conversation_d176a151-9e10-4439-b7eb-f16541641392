@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-edit me-3"></i>Edit Merchandise Package
                            </h2>
                            <p class="text-white mb-0">Update package information and pricing</p>
                        </div>
                        <div>
                            <a href="{{ route('admin.merchandise-packages.show', $merchandisePackage->id) }}"
                                class="btn btn-light me-2">
                                <i class="fas fa-eye me-2"></i>View
                            </a>
                            <a href="{{ route('admin.merchandise-packages.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>Package Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.merchandise-packages.update', $merchandisePackage->id) }}"
                        method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="mb-4">
                            <label for="product_name" class="form-label fw-semibold">
                                <i class="fas fa-tag me-2 text-primary"></i>Product Name *
                            </label>
                            <input type="text" class="form-control @error('product_name') is-invalid @enderror"
                                id="product_name" name="product_name"
                                value="{{ old('product_name', $merchandisePackage->product_name) }}"
                                placeholder="Enter product name" required>
                            @error('product_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="data_plan" class="form-label fw-semibold">
                                    <i class="fas fa-wifi me-2 text-info"></i>Data Plan *
                                </label>
                                <input type="text" class="form-control @error('data_plan') is-invalid @enderror"
                                    id="data_plan" name="data_plan"
                                    value="{{ old('data_plan', $merchandisePackage->data_plan) }}"
                                    placeholder="e.g., 5GB, Unlimited, etc." required>
                                @error('data_plan')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="price" class="form-label fw-semibold">
                                    <i class="fas fa-dollar-sign me-2 text-success"></i>Price *
                                </label>
                                <input type="number" class="form-control @error('price') is-invalid @enderror"
                                    id="price" name="price" value="{{ old('price', $merchandisePackage->price) }}"
                                    step="0.01" min="0" placeholder="0.00" required>
                                @error('price')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="duration_days" class="form-label fw-semibold">
                                    <i class="fas fa-calendar-days me-2 text-warning"></i>Duration (Days) *
                                </label>
                                <input type="number" class="form-control @error('duration_days') is-invalid @enderror"
                                    id="duration_days" name="duration_days"
                                    value="{{ old('duration_days', $merchandisePackage->duration_days) }}" min="1"
                                    placeholder="30" required>
                                @error('duration_days')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Number of days the package is valid</div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="image" class="form-label fw-semibold">
                                    <i class="fas fa-image me-2 text-secondary"></i>Upload Image
                                </label>
                                <input type="file" class="form-control @error('image') is-invalid @enderror" id="image"
                                    name="image" accept="image/*">
                                @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Optional: Upload the package image (JPEG, PNG, GIF, WebP - Max
                                    5MB)</div>
                                @if($merchandisePackage->image_url)
                                <div class="mt-2">
                                    <small class="text-muted">Current image:</small><br>
                                    <img src="{{ $merchandisePackage->image_url }}" alt="Current image"
                                        class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                </div>
                                @endif
                                <div id="image-preview" class="mt-2" style="display: none;">
                                    <small class="text-muted">New image preview:</small><br>
                                    <img id="preview-image" src="" alt="Preview" class="img-thumbnail"
                                        style="max-width: 200px; max-height: 200px;">
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label fw-semibold">
                                <i class="fas fa-align-left me-2 text-secondary"></i>Description
                            </label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description"
                                name="description" rows="4"
                                placeholder="Enter package description and features">{{ old('description', $merchandisePackage->description) }}</textarea>
                            @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="product_url" class="form-label fw-semibold">
                                <i class="fas fa-link me-2 text-info"></i>Product URL
                            </label>
                            <input type="url" class="form-control @error('product_url') is-invalid @enderror"
                                id="product_url" name="product_url"
                                value="{{ old('product_url', $merchandisePackage->product_url) }}"
                                placeholder="https://example.com/product">
                            @error('product_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Optional: External link to the product page</div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                    value="1" {{ old('is_active', $merchandisePackage->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label fw-semibold" for="is_active">
                                    <i class="fas fa-toggle-on me-2 text-success"></i>Active Status
                                </label>
                                <div class="form-text">Enable this package to be available for purchase</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.merchandise-packages.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Package
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on first input
    document.getElementById('product_name').focus();

    // Price formatting
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('blur', function() {
        if (this.value) {
            this.value = parseFloat(this.value).toFixed(2);
        }
    });

    // Image upload preview
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('image-preview');
    const previewImage = document.getElementById('preview-image');

    imageInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB');
                this.value = '';
                imagePreview.style.display = 'none';
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid image file (JPEG, PNG, GIF, WebP)');
                this.value = '';
                imagePreview.style.display = 'none';
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
        }
    });
});
</script>
@endsection