@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-crown me-3"></i>Previous Winners Management
                            </h2>
                            <p class="text-white mb-0">Showcase past campaign winners with video content</p>
                        </div>
                        <a href="{{ route('admin.previous-winners.create') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-plus me-2"></i>Add New Winner
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.previous-winners.index') }}">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">Search</label>
                                <input type="text" name="search" class="form-control"
                                    placeholder="Search by event name or winner name..."
                                    value="{{ request('search') }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label fw-semibold">Status</label>
                                <select name="is_active" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="1" {{ request('is_active')==='1' ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ request('is_active')==='0' ? 'selected' : '' }}>Inactive
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Previous Winners List
                        <span class="badge bg-light text-dark ms-2">{{ $previousWinners->total() }} Total</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if($previousWinners->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Event Name</th>
                                    <th>Winner</th>
                                    <th>Campaign</th>
                                    <th>Video</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($previousWinners as $winner)
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ $winner->event_name }}</div>
                                        @if($winner->description)
                                        <small class="text-muted">{{ Str::limit($winner->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($winner->winner_name)
                                        <div class="fw-semibold">{{ $winner->winner_name }}</div>
                                        @else
                                        <span class="text-muted">Not specified</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($winner->event && $winner->event->period)
                                        <div class="fw-semibold">{{ $winner->event->period }} Campaign</div>
                                        <small class="text-muted">{{ $winner->event->event_name }}</small>
                                        @else
                                        <span class="text-muted">Not specified</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($winner->video_url)
                                        <a href="{{ $winner->video_url }}" target="_blank"
                                            class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-play me-1"></i>Watch
                                        </a>
                                        @else
                                        <span class="text-muted">No video</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($winner->is_active)
                                        <span class="badge bg-success">Active</span>
                                        @else
                                        <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('admin.previous-winners.show', $winner->id) }}"
                                                class="btn btn-sm btn-info" title="View Winner Details">
                                                <i class="fas fa-eye me-1"></i>
                                                <span class="d-none d-md-inline">View</span>
                                            </a>
                                            <a href="{{ route('admin.previous-winners.edit', $winner->id) }}"
                                                class="btn btn-sm btn-warning" title="Edit Winner">
                                                <i class="fas fa-edit me-1"></i>
                                                <span class="d-none d-md-inline">Edit</span>
                                            </a>
                                            <form action="{{ route('admin.previous-winners.destroy', $winner->id) }}"
                                                method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger"
                                                    onclick="return confirm('Are you sure you want to delete this winner record?')"
                                                    title="Delete Winner">
                                                    <i class="fas fa-trash me-1"></i>
                                                    <span class="d-none d-md-inline">Delete</span>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $previousWinners->withQueryString()->links() }}
                    </div>
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-crown fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No previous winners found</h5>
                        <p class="text-muted">Start by adding your first winner record.</p>
                        <a href="{{ route('admin.previous-winners.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Winner
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when filters change
        const filterForm = document.querySelector('form[action*="previous-winners.index"]');
        const selectFilters = filterForm.querySelectorAll('select[name="is_active"]');

        selectFilters.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });

        // Add search delay for better UX
        const searchInput = filterForm.querySelector('input[name="search"]');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterForm.submit();
            }, 500); // 500ms delay
        });

        // Clear filters functionality
        const clearBtn = document.getElementById('clear-filters');
        if (clearBtn) {
            clearBtn.addEventListener('click', function() {
                filterForm.querySelectorAll('input, select').forEach(input => {
                    if (input.type === 'text' || input.type === 'search') {
                        input.value = '';
                    } else if (input.tagName === 'SELECT') {
                        input.selectedIndex = 0;
                    }
                });
                filterForm.submit();
            });
        }
    });
</script>
@endsection