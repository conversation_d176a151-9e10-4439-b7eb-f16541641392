<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Special Quota Form Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Special Quota Form Test</h2>
        
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-semibold text-info">Key-Value Details</span>
                    <button type="button" class="btn btn-sm btn-info" id="add-detail-btn">
                        <i class="fas fa-plus me-1"></i>Add Detail
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="details-container">
                    <!-- Details will be added here by JavaScript -->
                </div>
                <div id="debug-info" class="mt-2 small text-success">
                    Debug: Waiting for initialization...
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <button type="button" class="btn btn-primary" onclick="testForm()">Test Form</button>
            <button type="button" class="btn btn-secondary" onclick="addTestDetail()">Add Test Detail</button>
        </div>
    </div>

    <script>
        console.log('Test page script loading...');
        
        let detailIndex = 0;
        
        const defaultDetails = [
            { label: 'Kuota Utuh', value: '' },
            { label: 'Masa Aktif', value: '' }
        ];

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function createDetailRow(label = '', value = '', index = null) {
            if (index === null) {
                index = detailIndex++;
            }

            const escapedLabel = escapeHtml(label);
            const escapedValue = escapeHtml(value);

            return `
                <div class="detail-row mb-3 p-3 border rounded bg-light" data-index="${index}">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label class="form-label fw-semibold small">Label</label>
                            <input type="text" 
                                   class="form-control form-control-sm" 
                                   name="details[${index}][label]" 
                                   value="${escapedLabel}" 
                                   placeholder="e.g., Kuota Utuh"
                                   required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold small">Value</label>
                            <input type="text" 
                                   class="form-control form-control-sm" 
                                   name="details[${index}][value]" 
                                   value="${escapedValue}" 
                                   placeholder="e.g., 50 GB"
                                   required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small">&nbsp;</label>
                            <button type="button" class="btn btn-sm btn-outline-danger d-block remove-detail-btn" title="Remove Detail">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function addDetailRow(label = '', value = '') {
            console.log('addDetailRow called with:', { label, value });
            
            const container = document.getElementById('details-container');
            if (!container) {
                console.error('Details container not found');
                return false;
            }
            
            try {
                const newRow = createDetailRow(label, value);
                container.insertAdjacentHTML('beforeend', newRow);
                
                const newRowElement = container.lastElementChild;
                const removeBtn = newRowElement.querySelector('.remove-detail-btn');
                if (removeBtn) {
                    removeBtn.addEventListener('click', function() {
                        const allRows = document.querySelectorAll('.detail-row');
                        if (allRows.length > 1) {
                            newRowElement.remove();
                            console.log('Detail row removed');
                        } else {
                            alert('At least one detail is required.');
                        }
                    });
                }
                
                console.log('Detail row added successfully');
                return true;
            } catch (error) {
                console.error('Error adding detail row:', error);
                return false;
            }
        }

        function testForm() {
            console.log('Testing form functionality...');
            console.log('Details container exists:', !!document.getElementById('details-container'));
            console.log('Add button exists:', !!document.getElementById('add-detail-btn'));
            console.log('Current detail rows:', document.querySelectorAll('.detail-row').length);
            
            const debugInfo = document.getElementById('debug-info');
            if (debugInfo) {
                debugInfo.innerHTML = `
                    Container: ${!!document.getElementById('details-container')}<br>
                    Add Button: ${!!document.getElementById('add-detail-btn')}<br>
                    Detail Rows: ${document.querySelectorAll('.detail-row').length}
                `;
            }
        }

        function addTestDetail() {
            const success = addDetailRow('Test Label', 'Test Value');
            console.log('Add test detail result:', success);
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');
            
            const container = document.getElementById('details-container');
            const debugInfo = document.getElementById('debug-info');
            
            if (!container) {
                console.error('Container not found');
                if (debugInfo) debugInfo.textContent = 'Error: Container not found';
                return;
            }
            
            // Add default details
            defaultDetails.forEach(detail => {
                addDetailRow(detail.label, detail.value);
            });
            
            // Add button event listener
            const addBtn = document.getElementById('add-detail-btn');
            if (addBtn) {
                addBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Add button clicked');
                    addDetailRow();
                });
                if (debugInfo) debugInfo.textContent = 'Debug: Initialized successfully';
            } else {
                console.error('Add button not found');
                if (debugInfo) debugInfo.textContent = 'Error: Add button not found';
            }
        });
    </script>
</body>
</html>
