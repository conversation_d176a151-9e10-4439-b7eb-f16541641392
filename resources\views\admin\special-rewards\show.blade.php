@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-gift me-3"></i>Special Reward Details
                            </h2>
                            <p class="text-white mb-0">View reward information and validity period</p>
                        </div>
                        <div class="action-buttons">
                            <a href="{{ route('admin.special-rewards.edit', $specialReward->id) }}"
                                class="btn btn-light">
                                <i class="fas fa-edit me-2"></i>
                                <span class="d-none d-md-inline">Edit</span>
                            </a>
                            <a href="{{ route('admin.special-rewards.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>
                                <span class="d-none d-md-inline">Back to List</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reward Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header animated-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Reward Information
                        </h5>
                        <div>
                            @if($specialReward->is_active)
                            <span class="badge bg-success fs-6">Active</span>
                            @else
                            <span class="badge bg-danger fs-6">Inactive</span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-gift me-2 text-primary"></i>Reward Name
                            </label>
                            <div class="fs-4 fw-bold">{{ $specialReward->reward_name }}</div>
                        </div>
                    </div>

                    @if($specialReward->price)
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-dollar-sign me-2 text-success"></i>Price
                            </label>
                            <div class="fs-5 fw-semibold">${{ number_format($specialReward->price, 2) }}</div>
                        </div>
                    </div>
                    @else
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-gift me-2 text-success"></i>Price
                            </label>
                            <div class="fs-5 fw-semibold text-success">FREE</div>
                        </div>
                    </div>
                    @endif

                    @if($specialReward->description)
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-align-left me-2 text-secondary"></i>Description
                            </label>
                            <div class="fs-6">{{ $specialReward->description }}</div>
                        </div>
                    </div>
                    @endif

                    @if($specialReward->product_url)
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-link me-2 text-info"></i>Product URL
                            </label>
                            <div class="fs-6">
                                <a href="{{ $specialReward->product_url }}" target="_blank"
                                    class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-external-link-alt me-2"></i>Visit Product Page
                                </a>
                                <div class="text-muted small mt-1">{{ $specialReward->product_url }}</div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-plus-circle me-2 text-primary"></i>Created At
                                </label>
                                <div class="fs-6">{{ $specialReward->created_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-edit me-2 text-warning"></i>Last Updated
                                </label>
                                <div class="fs-6">{{ $specialReward->updated_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            @if($specialReward->image_url)
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-image me-2"></i>Reward Image
                    </h5>
                </div>
                <div class="card-body text-center">
                    <img src="{{ $specialReward->image_url }}" alt="{{ $specialReward->reward_name }}"
                        class="img-fluid rounded mb-3" style="max-height: 300px; object-fit: cover;">
                    <div class="mt-3">
                        <small class="text-muted">{{ $specialReward->image_url }}</small>
                    </div>
                </div>
            </div>
            @else
            <div class="card">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-image me-2"></i>Reward Image
                    </h5>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No image available</h6>
                    <p class="text-muted small">No image URL has been provided for this reward.</p>
                </div>
            </div>
            @endif

            <!-- Validity Status Card -->
            <div class="card mt-4">
                <div class="card-header animated-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Validity Status
                    </h5>
                </div>
                <div class="card-body text-center">
                    @php
                    $status = $specialReward->validity_status;
                    $isValid = $status !== 'expired';

                    switch($status) {
                    case 'expired':
                    $statusMessage = 'Expired';
                    $statusClass = 'text-danger';
                    break;
                    case 'expiring_soon':
                    $statusMessage = 'Expiring Soon';
                    $statusClass = 'text-warning';
                    break;
                    default:
                    $statusMessage = 'Active';
                    $statusClass = 'text-success';
                    }
                    @endphp

                    <i
                        class="fas fa-{{ $isValid ? 'check-circle' : 'times-circle' }} fa-3x {{ $statusClass }} mb-3"></i>
                    <h6 class="{{ $statusClass }}">{{ $statusMessage }}</h6>

                    <div class="row text-center mt-3">
                        <div class="col-md-4">
                            <div class="border-end">
                                <h6 class="text-muted mb-1">Validity Period</h6>
                                <p class="fw-bold">{{ $specialReward->validity_period_days }} days</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border-end">
                                <h6 class="text-muted mb-1">Expires On</h6>
                                <p class="fw-bold">{{ $specialReward->expiration_date->format('M d, Y') }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-muted mb-1">Status</h6>
                            <p class="fw-bold {{ $statusClass }}">{{ $specialReward->formatted_validity }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex flex-column flex-md-row justify-content-between gap-3">
                        <a href="{{ route('admin.special-rewards.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                        <div class="action-buttons">
                            <a href="{{ route('admin.special-rewards.edit', $specialReward->id) }}"
                                class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Edit Reward
                            </a>
                            <form action="{{ route('admin.special-rewards.destroy', $specialReward->id) }}"
                                method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger"
                                    onclick="return confirm('Are you sure you want to delete this reward?')">
                                    <i class="fas fa-trash me-2"></i>Delete Reward
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<style>
    .info-item {
        padding: 1rem;
        background: linear-gradient(135deg, rgba(135, 206, 235, 0.1) 0%, rgba(173, 216, 230, 0.1) 100%);
        border-radius: 10px;
        border-left: 4px solid var(--bubblegum-sky);
        transition: all 0.3s ease;
    }

    .info-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
</style>
@endsection