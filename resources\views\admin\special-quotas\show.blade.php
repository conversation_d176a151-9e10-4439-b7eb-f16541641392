@extends('admin.layout.master')

@section('title', 'Special Quota Details')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <div class="pc-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('admin.events.index') }}">Home</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('admin.special-quotas.index') }}">Special
                                    Quotas</a></li>
                            <li class="breadcrumb-item" aria-current="page">Details</li>
                        </ul>
                    </div>
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h2 class="mb-0">Special Quota Details</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row justify-content-center">
            <div class="col-xl-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-percentage me-2"></i>{{ $specialQuota->quota_name }}
                        </h5>
                        <div class="btn-group">
                            <a href="{{ route('admin.special-quotas.edit', $specialQuota->id) }}"
                                class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Edit
                            </a>
                            <a href="{{ route('admin.special-quotas.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-tag me-2 text-primary"></i>Quota Name
                                    </label>
                                    <div class="fs-5 fw-semibold">{{ $specialQuota->quota_name }}</div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-award me-2 text-warning"></i>Display Badge
                                    </label>
                                    <div class="fs-6">
                                        <span class="badge bg-info fs-6">{{ $specialQuota->display_badge }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-database me-2 text-info"></i>Quota Volume
                                    </label>
                                    <div class="fs-5 fw-semibold">{{ number_format($specialQuota->quota_volume) }}</div>
                                    <small class="text-muted">units</small>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-calendar-days me-2 text-success"></i>Validity Period
                                    </label>
                                    <div class="fs-5 fw-semibold">{{ $specialQuota->validity_days }}</div>
                                    <small class="text-muted">days</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-money-bill me-2 text-secondary"></i>Normal Price
                                    </label>
                                    <div class="fs-5 fw-semibold text-muted text-decoration-line-through">
                                        {{ $specialQuota->formatted_normal_price }}
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-tags me-2 text-danger"></i>Special Price
                                    </label>
                                    <div class="fs-4 fw-bold text-success">
                                        {{ $specialQuota->formatted_special_price }}
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-percentage me-2 text-warning"></i>Discount
                                    </label>
                                    <div class="fs-5 fw-bold text-danger">
                                        {{ $specialQuota->discount_percentage }}% OFF
                                    </div>
                                    <small class="text-muted">Save {{ $specialQuota->formatted_savings }}</small>
                                </div>
                            </div>
                        </div>



                        @if(count($specialQuota->description_details) > 0)
                        <div class="mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-list me-2 text-info"></i>Quota Details
                                </label>
                                <div class="card border-info">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0 text-info fw-semibold">
                                            <i class="fas fa-info-circle me-2"></i>Package Specifications
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @foreach($specialQuota->description_details as $index => $detail)
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="detail-item p-3 border rounded bg-light h-100">
                                                    <div class="d-flex align-items-center">
                                                        <div class="detail-icon me-3">
                                                            <i class="fas fa-tag text-info"></i>
                                                        </div>
                                                        <div class="detail-content flex-grow-1">
                                                            <div class="detail-label fw-semibold text-muted small">
                                                                {{ $detail['label'] ?? 'N/A' }}
                                                            </div>
                                                            <div class="detail-value fw-bold text-dark">
                                                                {{ $detail['value'] ?? 'N/A' }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        @if($specialQuota->product_url)
                        <div class="mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-link me-2 text-primary"></i>Product URL
                                </label>
                                <div class="fs-6">
                                    <a href="{{ $specialQuota->product_url }}" target="_blank" rel="noopener noreferrer"
                                        class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-external-link-alt me-2"></i>View Product Page
                                    </a>
                                </div>
                                <small class="text-muted">{{ $specialQuota->product_url }}</small>
                            </div>
                        </div>
                        @endif

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-toggle-on me-2 text-success"></i>Status
                                    </label>
                                    <div class="fs-6">
                                        @if($specialQuota->is_active)
                                        <span class="badge bg-success fs-6">Active</span>
                                        @else
                                        <span class="badge bg-secondary fs-6">Inactive</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            @if($specialQuota->event)
                            <div class="col-md-6 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-calendar me-2 text-info"></i>Event
                                    </label>
                                    <div class="fs-6 fw-semibold">{{ $specialQuota->event->event_name }}</div>
                                </div>
                            </div>
                            @endif
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-clock me-2 text-info"></i>Created At
                                    </label>
                                    <div class="fs-6">{{ $specialQuota->created_at->format('F d, Y H:i') }}</div>
                                    <small class="text-muted">{{ $specialQuota->created_at->diffForHumans() }}</small>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="info-item">
                                    <label class="fw-bold text-muted mb-2">
                                        <i class="fas fa-edit me-2 text-warning"></i>Last Updated
                                    </label>
                                    <div class="fs-6">{{ $specialQuota->updated_at->format('F d, Y H:i') }}</div>
                                    <small class="text-muted">{{ $specialQuota->updated_at->diffForHumans() }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Rich Text Display Styling */
    .rich-text-display {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e3e6f0 !important;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .rich-text-display::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #4e73df, #36b9cc, #1cc88a);
    }

    .rich-text-display:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
        transform: translateY(-1px);
    }

    .rich-text-content {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.7;
        color: #5a5c69;
        font-size: 15px;
    }

    /* Typography Enhancements */
    .rich-text-content h1,
    .rich-text-content h2,
    .rich-text-content h3,
    .rich-text-content h4,
    .rich-text-content h5,
    .rich-text-content h6 {
        color: #2c3e50;
        font-weight: 600;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
        line-height: 1.3;
    }

    .rich-text-content h1 {
        font-size: 1.75rem;
    }

    .rich-text-content h2 {
        font-size: 1.5rem;
    }

    .rich-text-content h3 {
        font-size: 1.25rem;
    }

    .rich-text-content h4 {
        font-size: 1.1rem;
    }

    .rich-text-content h5 {
        font-size: 1rem;
    }

    .rich-text-content h6 {
        font-size: 0.9rem;
    }

    .rich-text-content p {
        margin-bottom: 1rem;
        text-align: justify;
    }

    .rich-text-content p:last-child {
        margin-bottom: 0;
    }

    /* List Styling */
    .rich-text-content ul,
    .rich-text-content ol {
        margin: 1rem 0;
        padding-left: 2rem;
    }

    .rich-text-content ul li {
        position: relative;
        margin-bottom: 0.5rem;
        list-style: none;
    }

    .rich-text-content ul li::before {
        content: '▶';
        color: #4e73df;
        font-size: 0.8rem;
        position: absolute;
        left: -1.5rem;
        top: 0.1rem;
    }

    .rich-text-content ol li {
        margin-bottom: 0.5rem;
        color: #5a5c69;
    }

    .rich-text-content ol li::marker {
        color: #4e73df;
        font-weight: 600;
    }

    /* Text Formatting */
    .rich-text-content strong,
    .rich-text-content b {
        color: #2c3e50;
        font-weight: 600;
    }

    .rich-text-content em,
    .rich-text-content i {
        color: #6c757d;
        font-style: italic;
    }

    .rich-text-content u {
        text-decoration: underline;
        text-decoration-color: #4e73df;
        text-decoration-thickness: 2px;
        text-underline-offset: 2px;
    }

    /* Links */
    .rich-text-content a {
        color: #4e73df;
        text-decoration: none;
        font-weight: 500;
        border-bottom: 1px solid transparent;
        transition: all 0.3s ease;
    }

    .rich-text-content a:hover {
        color: #2653d4;
        border-bottom-color: #4e73df;
    }

    /* Blockquotes */
    .rich-text-content blockquote {
        border-left: 4px solid #4e73df;
        margin: 1.5rem 0;
        padding: 1rem 1.5rem;
        background: #f8f9fc;
        border-radius: 0 8px 8px 0;
        font-style: italic;
        color: #6c757d;
        position: relative;
    }

    .rich-text-content blockquote::before {
        content: '"';
        font-size: 3rem;
        color: #4e73df;
        position: absolute;
        top: -0.5rem;
        left: 0.5rem;
        opacity: 0.3;
    }

    /* Tables */
    .rich-text-content table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .rich-text-content table th,
    .rich-text-content table td {
        padding: 0.75rem 1rem;
        text-align: left;
        border-bottom: 1px solid #e3e6f0;
    }

    .rich-text-content table th {
        background: #4e73df;
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }

    .rich-text-content table tr:hover {
        background: #f8f9fc;
    }

    /* Code Styling */
    .rich-text-content code {
        background: #f1f2f6;
        color: #e83e8c;
        padding: 0.2rem 0.4rem;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.9em;
    }

    .rich-text-content pre {
        background: #2d3748;
        color: #e2e8f0;
        padding: 1.5rem;
        border-radius: 8px;
        overflow-x: auto;
        margin: 1.5rem 0;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        line-height: 1.5;
    }

    .rich-text-content pre code {
        background: none;
        color: inherit;
        padding: 0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .rich-text-content {
            font-size: 14px;
        }

        .rich-text-content h1 {
            font-size: 1.5rem;
        }

        .rich-text-content h2 {
            font-size: 1.3rem;
        }

        .rich-text-content h3 {
            font-size: 1.1rem;
        }

        .rich-text-content ul,
        .rich-text-content ol {
            padding-left: 1.5rem;
        }

        .rich-text-content table {
            font-size: 0.9rem;
        }

        .rich-text-content table th,
        .rich-text-content table td {
            padding: 0.5rem 0.75rem;
        }
    }

    /* Print Styles */
    @media print {
        .rich-text-display {
            box-shadow: none !important;
            border: 1px solid #ccc !important;
            background: white !important;
        }

        .rich-text-display::before {
            display: none;
        }

        .rich-text-content {
            color: black !important;
        }

        .rich-text-content a {
            color: black !important;
            text-decoration: underline !important;
        }
    }

    /* Detail Items Styling */
    .detail-item {
        transition: all 0.3s ease;
        border: 1px solid #e3e6f0 !important;
    }

    .detail-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: #4e73df !important;
    }

    .detail-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #4e73df, #36b9cc);
        border-radius: 50%;
        color: white;
        font-size: 16px;
    }

    .detail-label {
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 2px;
    }

    .detail-value {
        font-size: 1rem;
        color: #2c3e50 !important;
    }

    /* Responsive adjustments for details */
    @media (max-width: 768px) {
        .detail-item {
            margin-bottom: 1rem;
        }

        .detail-icon {
            width: 35px;
            height: 35px;
            font-size: 14px;
        }

        .detail-value {
            font-size: 0.9rem;
        }
    }
</style>
@endpush