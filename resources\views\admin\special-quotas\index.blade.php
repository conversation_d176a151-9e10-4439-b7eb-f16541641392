@extends('admin.layout.master')

@section('title', 'Special Quotas Management')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <div class="pc-content">
        <!-- [ breadcrumb ] start -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <ul class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('admin.events.index') }}">Home</a></li>
                            <li class="breadcrumb-item" aria-current="page">Special Quotas</li>
                        </ul>
                    </div>
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h2 class="mb-0">Special Quotas Management</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ breadcrumb ] end -->

        <!-- [ Main Content ] start -->
        <div class="row">
            <div class="col-xl-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-percentage me-2"></i>Special Quotas List
                        </h5>
                        <a href="{{ route('admin.special-quotas.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add New Quota
                        </a>
                    </div>
                    <div class="card-body">
                        <!-- Search and Filter Form -->
                        <form method="GET" action="{{ route('admin.special-quotas.index') }}" class="mb-4">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="search" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="search" name="search"
                                            value="{{ request('search') }}"
                                            placeholder="Search by quota name, badge, or description...">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="">All Status</option>
                                            <option value="1" {{ request('status')==='1' ? 'selected' : '' }}>Active
                                            </option>
                                            <option value="0" {{ request('status')==='0' ? 'selected' : '' }}>Inactive
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-outline-primary">
                                                <i class="fas fa-search me-1"></i>Search
                                            </button>
                                            <a href="{{ route('admin.special-quotas.index') }}"
                                                class="btn btn-outline-secondary">
                                                <i class="fas fa-times me-1"></i>Clear
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        @if($specialQuotas->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Quota Name</th>
                                        <th>Badge</th>
                                        <th>Volume</th>
                                        <th>Validity</th>
                                        <th>Pricing</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($specialQuotas as $quota)
                                    <tr>
                                        <td>
                                            <div class="fw-bold">{{ $quota->quota_name }}</div>
                                            @if(count($quota->description_details) > 0)
                                            <div class="mt-1">
                                                @foreach(array_slice($quota->description_details, 0, 3) as $detail)
                                                <span class="badge bg-light text-dark me-1 small">
                                                    {{ $detail['label'] ?? '' }}: {{ $detail['value'] ?? '' }}
                                                </span>
                                                @endforeach
                                                @if(count($quota->description_details) > 3)
                                                <span class="badge bg-secondary small">+{{
                                                    count($quota->description_details) - 3 }} more</span>
                                                @endif
                                            </div>
                                            @else
                                            <small class="text-muted">No details configured</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $quota->display_badge }}</span>
                                        </td>
                                        <td>
                                            <div class="fw-bold">{{ number_format($quota->quota_volume) }}</div>
                                            <small class="text-muted">units</small>
                                        </td>
                                        <td>
                                            <div class="fw-bold">{{ $quota->validity_days }}</div>
                                            <small class="text-muted">days</small>
                                        </td>
                                        <td>
                                            <div class="fw-bold text-success">{{ $quota->formatted_special_price }}
                                            </div>
                                            <small class="text-muted text-decoration-line-through">{{
                                                $quota->formatted_normal_price }}</small>
                                            <div class="small text-danger">Save {{ $quota->discount_percentage }}%</div>
                                        </td>
                                        <td>
                                            @if($quota->is_active)
                                            <span class="badge bg-success">Active</span>
                                            @else
                                            <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.special-quotas.show', $quota->id) }}"
                                                    class="btn btn-outline-info btn-sm" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.special-quotas.edit', $quota->id) }}"
                                                    class="btn btn-outline-warning btn-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('admin.special-quotas.destroy', $quota->id) }}"
                                                    method="POST" class="d-inline"
                                                    onsubmit="return confirm('Are you sure you want to delete this quota?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-outline-danger btn-sm"
                                                        title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $specialQuotas->appends(request()->query())->links() }}
                        </div>
                        @else
                        <div class="text-center py-5">
                            <i class="fas fa-percentage fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Special Quotas Found</h5>
                            <p class="text-muted">Start by creating your first special quota.</p>
                            <a href="{{ route('admin.special-quotas.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add New Quota
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <!-- [ Main Content ] end -->
    </div>
</div>
@endsection