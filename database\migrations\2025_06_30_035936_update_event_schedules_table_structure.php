<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('event_schedules', function (Blueprint $table) {
            // Remove time fields
            $table->dropColumn(['start_time', 'end_time']);

            // Add event_end_date field as nullable first
            $table->date('event_end_date')->nullable()->after('event_date');
        });

        // Update existing records to set event_end_date same as event_date
        DB::table('event_schedules')
            ->whereNull('event_end_date')
            ->update(['event_end_date' => DB::raw('event_date')]);

        // Now make the column NOT NULL
        Schema::table('event_schedules', function (Blueprint $table) {
            $table->date('event_end_date')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('event_schedules', function (Blueprint $table) {
            // Add back time fields
            $table->time('start_time')->nullable()->after('location');
            $table->time('end_time')->nullable()->after('start_time');

            // Remove event_end_date field
            $table->dropColumn('event_end_date');
        });
    }
};
