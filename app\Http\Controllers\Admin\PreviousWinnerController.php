<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\EventFilterTrait;
use App\Models\PreviousWinner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;

class PreviousWinnerController extends Controller
{
    use EventFilterTrait;
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (!in_array(Auth::user()->role, ['superadmin', 'admin'])) {
                abort(403, 'Access Denied. Only Admin roles are allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = PreviousWinner::query();

        // Apply event filter
        $query = $this->applyEventFilter($query);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('event_name', 'like', '%' . $search . '%')
                  ->orWhere('winner_name', 'like', '%' . $search . '%');
            });
        }

        // Filter by active status
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $previousWinners = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.previous-winners.index', compact('previousWinners'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.previous-winners.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if an event is selected
        $redirectResponse = $this->requireActiveEvent();
        if ($redirectResponse) {
            Alert::warning('Warning', 'Please select an event first before creating new content.');
            return $redirectResponse;
        }

        $validated = $request->validate([
            'event_name' => 'required|string|max:255',
            'video_url' => 'nullable|url',
            'winner_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        // Set event_id for new record
        $this->setEventIdForNewRecord($validated);

        PreviousWinner::create($validated);
        Alert::success('Success', 'Previous winner created successfully!');

        return redirect()->route('admin.previous-winners.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $previousWinner = PreviousWinner::findOrFail($id);
        return view('admin.previous-winners.show', compact('previousWinner'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $previousWinner = PreviousWinner::findOrFail($id);
        return view('admin.previous-winners.edit', compact('previousWinner'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $previousWinner = PreviousWinner::findOrFail($id);

        $validated = $request->validate([
            'event_name' => 'required|string|max:255',
            'video_url' => 'nullable|url',
            'winner_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        $previousWinner->update($validated);
        Alert::success('Success', 'Previous winner updated successfully!');

        return redirect()->route('admin.previous-winners.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $previousWinner = PreviousWinner::findOrFail($id);
            $previousWinner->delete();
            Alert::success('Success', 'Previous winner deleted successfully!');
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to delete previous winner!');
        }

        return redirect()->route('admin.previous-winners.index');
    }
}
