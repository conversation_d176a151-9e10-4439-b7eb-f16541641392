@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <i class="fas fa-star me-2" style="color: #FF6B6B;"></i>
                                {{ $event->event_name }}
                            </h4>
                            <p class="text-muted mb-0">Event Details and Statistics</p>
                        </div>
                        <div class="btn-group">
                            <a href="{{ route('admin.events.edit', $event->id) }}" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>Edit Event
                            </a>
                            <a href="{{ route('admin.events.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Events
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Information -->
    <div class="row mb-4">
        <div class="col-lg-8 col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Event Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Event Name:</strong><br>
                                <span class="text-muted">{{ $event->event_name }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Period:</strong><br>
                                <span class="badge bg-info">{{ $event->period }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Description:</strong><br>
                        <span class="text-muted">{{ $event->description ?: 'No description provided' }}</span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Status:</strong><br>
                                @if($event->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-secondary">Inactive</span>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Created:</strong><br>
                                <span class="text-muted">{{ $event->created_at->format('M d, Y H:i') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.event-schedules.create') }}" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-alt me-2"></i>Add Event Schedule
                        </a>
                        <a href="{{ route('admin.faqs.create') }}" class="btn btn-outline-success">
                            <i class="fas fa-question-circle me-2"></i>Add FAQ
                        </a>
                        <a href="{{ route('admin.finalists.create') }}" class="btn btn-outline-info">
                            <i class="fas fa-trophy me-2"></i>Add Finalist
                        </a>
                        <a href="{{ route('admin.previous-winners.create') }}" class="btn btn-outline-warning">
                            <i class="fas fa-crown me-2"></i>Add Winner
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-calendar-alt fa-2x" style="color: #FFB6C1;"></i>
                    </div>
                    <h4 class="mb-1">{{ $event->eventSchedules->count() }}</h4>
                    <small class="text-muted">Event Schedules</small>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-question-circle fa-2x" style="color: #DDA0DD;"></i>
                    </div>
                    <h4 class="mb-1">{{ $event->faqs->count() }}</h4>
                    <small class="text-muted">FAQs</small>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-trophy fa-2x" style="color: #98FB98;"></i>
                    </div>
                    <h4 class="mb-1">{{ $event->finalists->count() }}</h4>
                    <small class="text-muted">Finalists</small>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-crown fa-2x" style="color: #87CEEB;"></i>
                    </div>
                    <h4 class="mb-1">{{ $event->previousWinners->count() }}</h4>
                    <small class="text-muted">Previous Winners</small>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-box fa-2x" style="color: #FFCCCB;"></i>
                    </div>
                    <h4 class="mb-1">{{ $event->merchandisePackages->count() }}</h4>
                    <small class="text-muted">Merchandise</small>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-gift fa-2x" style="color: #FFB6D1;"></i>
                    </div>
                    <h4 class="mb-1">{{ $event->specialRewards->count() }}</h4>
                    <small class="text-muted">Special Rewards</small>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
