<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\EventFilterTrait;
use App\Http\Traits\FileUploadTrait;
use App\Models\Finalist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;

class FinalistController extends Controller
{
    use EventFilterTrait, FileUploadTrait;
    public function __construct()
    {
        $this->middleware('auth');

        // Add validation to check admin role
        $this->middleware(function ($request, $next) {
            if (!in_array(Auth::user()->role, ['superadmin', 'admin'])) {
                abort(403, 'Access Denied. Only Admin roles are allowed to access this page.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Finalist::query();

        // Apply event filter
        $query = $this->applyEventFilter($query);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('username', 'like', '%' . $search . '%')
                  ->orWhere('city', 'like', '%' . $search . '%');
            });
        }

        // Filter by city
        if ($request->has('city') && !empty($request->city)) {
            $query->where('city', $request->city);
        }

        // Filter by badge status
        if ($request->has('badge_status') && $request->badge_status !== '') {
            $query->where('badge_status', $request->boolean('badge_status'));
        }

        // Filter by active status
        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $finalists = $query->orderBy('rank', 'asc')->paginate(15);
        $cities = Finalist::distinct()->pluck('city');

        return view('admin.finalists.index', compact('finalists', 'cities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.finalists.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if an event is selected
        $redirectResponse = $this->requireActiveEvent();
        if ($redirectResponse) {
            Alert::warning('Warning', 'Please select an event first before creating new content.');
            return $redirectResponse;
        }

        $validated = $request->validate([
            'rank' => 'required|integer|min:1',
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:finalists,username',
            'photo' => array_merge(['nullable'], $this->getImageValidationRules()),
            'badge_status' => 'boolean',
            'city' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        // Handle file upload
        $this->handleFileUpload($validated, 'photo', 'finalists');

        // Set event_id for new record
        $this->setEventIdForNewRecord($validated);

        Finalist::create($validated);
        Alert::success('Success', 'Finalist created successfully!');

        return redirect()->route('admin.finalists.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $finalist = Finalist::findOrFail($id);
        return view('admin.finalists.show', compact('finalist'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $finalist = Finalist::findOrFail($id);
        return view('admin.finalists.edit', compact('finalist'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $finalist = Finalist::findOrFail($id);

        $validated = $request->validate([
            'rank' => 'required|integer|min:1',
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:finalists,username,' . $id,
            'photo' => array_merge(['nullable'], $this->getImageValidationRules()),
            'badge_status' => 'boolean',
            'city' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        // Handle file update
        $this->handleFileUpdate($validated, 'photo', $finalist->photo, 'finalists');

        $finalist->update($validated);
        Alert::success('Success', 'Finalist updated successfully!');

        return redirect()->route('admin.finalists.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $finalist = Finalist::findOrFail($id);
            $finalist->delete();
            Alert::success('Success', 'Finalist deleted successfully!');
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to delete finalist!');
        }

        return redirect()->route('admin.finalists.index');
    }
}
