<?php

namespace App\Http\Controllers\Employee;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use Alert;
use App\Models\Procurement;
use Illuminate\Http\Request;
use Auth;

class ProcurementController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        
        // Tambahkan validasi untuk memeriksa role admin
        $this->middleware(function ($request, $next) {
            if (Auth::user()->role == 'Admin') {
                abort(403, 'Akses Ditolak. Role Admin tidak diizinkan mengakses halaman ini.');
            }
            return $next($request);
        });
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $data['role'] = $user->role;
        
        $procurement = Procurement::query();
        
        if ($user->role == 'Employee') {
            // Jika role Employee, tampilkan hanya procurement yang dibuat oleh user tersebut
            $procurement->where('requester_user_id', $user->id);
        } elseif ($user->role == 'Supervisor') {
            // Jika role Supervisor, tampilkan hanya procurement yang menunggu persetujuan Supervisor
            $procurement->where('status', 'Waiting for Supervisor Approval');
        } elseif ($user->role == 'Manager') {
            // Jika role Manager, tampilkan hanya procurement yang menunggu persetujuan Manager
            $procurement->where('status', 'Waiting for Manager Approval');
        }
        
        $data['procurement'] = $procurement->latest()->get();
        return view('employee.procurement.index', $data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('employee.procurement.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $requestData = $request->all();
        $requestData['requester_user_id'] = Auth::id(); // Menambahkan ID user yang sedang login
        $requestData['supervisor_approval'] = 'Pending'; // Set persetujuan supervisor ke Pending
        $requestData['manager_approval'] = 'Pending'; // Set persetujuan manager ke Pending
        $requestData['status'] = 'Waiting for Supervisor Approval'; // Set status menunggu persetujuan supervisor
        
        Procurement::create($requestData);
        alert()->success('Pengajuan Procurement Baru Berhasil Dibuat!');

        return redirect('employee/procurement');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $procurement = Procurement::findOrFail($id);

        return view('employee.procurement.show', compact('procurement'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $procurement = Procurement::findOrFail($id);
        $data['procurement'] = $procurement;
        return view('employee.procurement.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        
        $requestData = $request->all();
        
        $procurement = Procurement::findOrFail($id);
        alert()->success('Record Updated!' );
        $procurement->update($requestData);

        return redirect('employee/procurement');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        alert()->success('Record Deleted!' );
        Procurement::destroy($id);

        return redirect('employee/procurement');
    }

    /**
     * Approve procurement by Supervisor/Manager
     *
     * @param  int  $id
     * @param  string  $role
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve($id)
    {
        $procurement = Procurement::findOrFail($id);
        $role = Auth::user()->role;
        $updateData = [];
        $message = '';
        
        if ($role == 'Supervisor') {
            $updateData = [
                'supervisor_approval' => 'Approved',
                'status' => 'Waiting for Manager Approval'
            ];
            $message = 'Pengajuan Procurement Disetujui Supervisor!';
        } 
        elseif ($role == 'Manager') {
            $updateData = [
                'manager_approval' => 'Approved',
                'status' => 'Approved'
            ];
            $message = 'Pengajuan Procurement Disetujui Manager!';
        }
        
        $procurement->update($updateData);
        alert()->success($message);
        return redirect('employee/procurement');
    }

    /**
     * Deny procurement by Supervisor/Manager
     *
     * @param  int  $id
     * @param  string  $role
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deny($id)
    {
        $procurement = Procurement::findOrFail($id);
        $role = Auth::user()->role;
        
        $updateData = [];
        $message = '';
        
        if ($role == 'Supervisor') {
            $updateData = [
                'supervisor_approval' => 'Denied',
                'status' => 'Denied by Supervisor'
            ];
            $message = 'Pengajuan Procurement Ditolak Supervisor!';
        } 
        elseif ($role == 'Manager') {
            $updateData = [
                'manager_approval' => 'Denied',
                'status' => 'Denied by Manager'
            ];
            $message = 'Pengajuan Procurement Ditolak Manager!';
        }
        
        $procurement->update($updateData);
        alert()->success($message);
        return redirect('employee/procurement');
    }
}
