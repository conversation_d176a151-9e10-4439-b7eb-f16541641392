<nav class="pc-sidebar">
    <div class="navbar-wrapper">
        <div class="m-header">
            <a href="{{ url('/admin/dashboard') }}" class="b-brand text-primary">
                <!-- ========   Change your logo from here   ============ -->
                <img src="{{ asset('logo.png') }}" style="max-width: 100%;
    height: auto;" alt="logo" />
            </a>
        </div>
        <div class="navbar-content">
            <div class="card pc-user-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <img src="{{ asset('vendor/dashboard') }}/assets/images/user/avatar-1.jpg" alt="user-image"
                                class="user-avtar wid-45 rounded-circle" />
                        </div>
                        <div class="flex-grow-1 ms-3 me-2">
                            <h6 class="mb-0">{{ Auth::user()->name }}</h6>
                            <small>Administrator</small>
                        </div>
                        <a class="btn btn-icon btn-link-secondary avtar" data-bs-toggle="collapse"
                            href="#pc_sidebar_userlink"><svg class="pc-icon">
                                <use xlink:href="#custom-sort-outline"></use>
                            </svg></a>
                    </div>
                    <div class="collapse pc-user-links" id="pc_sidebar_userlink">
                        <div class="pt-3">
                            <a href="#!"><i class="ti ti-user"></i>
                                <span>My Account</span> </a>
                            <a href="{{ route('sign-out') }}"><i class="ti ti-power"></i>
                                <span>Logout</span></a>
                        </div>
                    </div>
                </div>
            </div>
            <ul class="pc-navbar" style="display: contents !important;">
                <!-- Dashboard -->
                <li class="pc-item">
                    <a href="{{ url('/admin') }}" class="pc-link {{ request()->is('admin') ? 'active' : '' }}">
                        <span class="pc-micon" style="color: #FF69B4;"><i class="fas fa-tachometer-alt"></i></span>
                        <span class="pc-mtext">Dashboard</span>
                    </a>
                </li>

                <!-- Koreakaja CMS Section -->
                <li class="pc-item pc-caption">
                    <label>Korea Kaja CMS</label>
                    <svg class="pc-icon">
                        <use xlink:href="#custom-presentation-chart"></use>
                    </svg>
                </li>
                <li class="pc-item">
                    <a href="{{ route('admin.events.index') }}"
                        class="pc-link {{ request()->is('admin/events*') ? 'active' : '' }}">
                        <span class="pc-micon" style="color: #FF6B6B;"><i class="fas fa-star"></i></span>
                        <span class="pc-mtext">Events</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="{{ route('admin.event-schedules.index') }}"
                        class="pc-link {{ request()->is('admin/event-schedules*') ? 'active' : '' }}">
                        <span class="pc-micon" style="color: #FFB6C1;"><i class="fas fa-calendar-alt"></i></span>
                        <span class="pc-mtext">Event Schedules</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="{{ route('admin.faqs.index') }}"
                        class="pc-link {{ request()->is('admin/faqs*') ? 'active' : '' }}">
                        <span class="pc-micon" style="color: #DDA0DD;"><i class="fas fa-question-circle"></i></span>
                        <span class="pc-mtext">FAQs</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="{{ route('admin.finalists.index') }}"
                        class="pc-link {{ request()->is('admin/finalists*') ? 'active' : '' }}">
                        <span class="pc-micon" style="color: #98FB98;"><i class="fas fa-trophy"></i></span>
                        <span class="pc-mtext">Finalists</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="{{ route('admin.previous-winners.index') }}"
                        class="pc-link {{ request()->is('admin/previous-winners*') ? 'active' : '' }}">
                        <span class="pc-micon" style="color: #87CEEB;"><i class="fas fa-crown"></i></span>
                        <span class="pc-mtext">Previous Winners</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="{{ route('admin.merchandise-packages.index') }}"
                        class="pc-link {{ request()->is('admin/merchandise-packages*') ? 'active' : '' }}">
                        <span class="pc-micon" style="color: #FFCCCB;"><i class="fas fa-box"></i></span>
                        <span class="pc-mtext">Merchandise</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="{{ route('admin.special-rewards.index') }}"
                        class="pc-link {{ request()->is('admin/special-rewards*') ? 'active' : '' }}">
                        <span class="pc-micon" style="color: #FFB6D1;"><i class="fas fa-gift"></i></span>
                        <span class="pc-mtext">Special Rewards</span>
                    </a>
                </li>
                <li class="pc-item">
                    <a href="{{ route('admin.special-quotas.index') }}"
                        class="pc-link {{ request()->is('admin/special-quotas*') ? 'active' : '' }}">
                        <span class="pc-micon" style="color: #FFA500;"><i class="fas fa-percentage"></i></span>
                        <span class="pc-mtext">Special Quotas</span>
                    </a>
                </li>

                <!-- System Management Section -->
                <li class="pc-item pc-caption">
                    <label>System Management</label>
                    <svg class="pc-icon">
                        <use xlink:href="#custom-presentation-chart"></use>
                    </svg>
                </li>
                <li class="pc-item">
                    <a href="{{ url('/admin/user') }}"
                        class="pc-link {{ request()->is('admin/user*') ? 'active' : '' }}">
                        <span class="pc-micon"><i class="fas fa-users"></i></span>
                        <span class="pc-mtext">User Management</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>