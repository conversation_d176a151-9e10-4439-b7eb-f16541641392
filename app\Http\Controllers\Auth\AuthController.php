<?php

namespace App\Http\Controllers\Auth;

use RealRashid\SweetAlert\Facades\Alert;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;

class AuthController extends Controller
{
    public function loginpage() {
        return view('auth.login');
    }

    public function loginUser(Request $req)
    {
        $req->validate([
            'username' => 'required',
            'password' => 'required',
        ]);

        $username = $req->get('username');
        $password = $req->get('password');

        $user = User::where('username', $username)->first();
        if ($user && \Hash::check($password, $user->password)) {
            // Check user role and restrict to superadmin/admin only
            $role = $user->role;

            if (!in_array($role, ['superadmin', 'admin'])) {
                alert()->error('Access denied. Only admin users are allowed.');
                return redirect('/auth-user');
            }

            auth()->guard('web')->login($user);
            session(["username" => $username]);
            alert()->success('Login Berhasil');

            // Redirect to admin dashboard for both superadmin and admin roles
            return redirect('/admin');
        } else {
            alert()->error('Username atau Password Salah!');
            return redirect('/auth-user');
        }
    }

    public function logout() {
        session()->flush();
        Auth::logout();
        return redirect('login');
    }
}

