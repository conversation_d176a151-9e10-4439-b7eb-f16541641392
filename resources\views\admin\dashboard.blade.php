@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card"
                style="background: linear-gradient(135deg, #FFB6C1 0%, #DDA0DD 50%, #87CEEB 100%); border: none;">
                <div class="card-body text-center py-5">
                    <h1 class="text-white fw-bold mb-3">
                        🇰🇷🇮🇩 Welcome to Koreakaja CMS Dashboard 🇰🇷🇮🇩
                    </h1>
                    <p class="text-white fs-5 mb-0">
                        Manage your Koreakaja campaign content with style and ease
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card bounce-in" style="animation-delay: 0.1s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                style="width: 60px; height: 60px; background: linear-gradient(45deg, #FFB6C1, #FF69B4);">
                                <i class="fas fa-calendar-alt text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Event Schedules</h6>
                            <h3 class="mb-0 fw-bold">{{ \App\Models\EventSchedule::count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card bounce-in" style="animation-delay: 0.2s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                style="width: 60px; height: 60px; background: linear-gradient(45deg, #DDA0DD, #BA55D3);">
                                <i class="fas fa-question-circle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">FAQs</h6>
                            <h3 class="mb-0 fw-bold">{{ \App\Models\Faq::count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card bounce-in" style="animation-delay: 0.3s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                style="width: 60px; height: 60px; background: linear-gradient(45deg, #98FB98, #32CD32);">
                                <i class="fas fa-trophy text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Finalists</h6>
                            <h3 class="mb-0 fw-bold">{{ \App\Models\Finalist::count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card bounce-in" style="animation-delay: 0.4s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                style="width: 60px; height: 60px; background: linear-gradient(45deg, #87CEEB, #4682B4);">
                                <i class="fas fa-crown text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Previous Winners</h6>
                            <h3 class="mb-0 fw-bold">{{ \App\Models\PreviousWinner::count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row of Statistics -->
    <div class="row mb-4">
        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card bounce-in" style="animation-delay: 0.5s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                style="width: 60px; height: 60px; background: linear-gradient(45deg, #FFCCCB, #FFA07A);">
                                <i class="fas fa-box text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Merchandise Packages</h6>
                            <h3 class="mb-0 fw-bold">{{ \App\Models\MerchandisePackage::count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-md-6 mb-4">
            <div class="card bounce-in" style="animation-delay: 0.6s;">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                style="width: 60px; height: 60px; background: linear-gradient(45deg, #E6E6FA, #9370DB);">
                                <i class="fas fa-gift text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Special Rewards</h6>
                            <h3 class="mb-0 fw-bold">{{ \App\Models\SpecialReward::count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-rocket me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <a href="{{ route('admin.event-schedules.create') }}" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>Add Event
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <a href="{{ route('admin.faqs.create') }}" class="btn btn-success w-100">
                                <i class="fas fa-plus me-2"></i>Add FAQ
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <a href="{{ route('admin.finalists.create') }}" class="btn btn-info w-100">
                                <i class="fas fa-plus me-2"></i>Add Finalist
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <a href="{{ route('admin.previous-winners.create') }}" class="btn btn-warning w-100">
                                <i class="fas fa-plus me-2"></i>Add Winner
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <a href="{{ route('admin.merchandise-packages.create') }}" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>Add Package
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <a href="{{ route('admin.special-rewards.create') }}" class="btn btn-success w-100">
                                <i class="fas fa-plus me-2"></i>Add Reward
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection