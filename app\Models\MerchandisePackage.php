<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MerchandisePackage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'event_id',
        'product_name',
        'data_plan',
        'price',
        'image',
        'duration_days',
        'description',
        'product_url',
        'is_active'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'duration_days' => 'integer',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // Accessors
    public function getImageUrlAttribute()
    {
        if ($this->image && !str_starts_with($this->image, 'http')) {
            return asset('storage/' . $this->image);
        }
        return $this->image;
    }

    public function getFullImageUrlAttribute()
    {
        return $this->image_url;
    }
}
