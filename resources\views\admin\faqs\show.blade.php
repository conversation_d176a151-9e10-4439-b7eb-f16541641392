@extends('admin.layout.master')

@section('content')
<div class="content-inner container-fluid pb-0 fade-in" id="page_layout">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header-animated">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-white fw-bold mb-2">
                                <i class="fas fa-question-circle me-3"></i>FAQ Details
                            </h2>
                            <p class="text-white mb-0">View frequently asked question information</p>
                        </div>
                        <div>
                            <a href="{{ route('admin.faqs.edit', $faq->id) }}" class="btn btn-light me-2">
                                <i class="fas fa-edit me-2"></i>Edit
                            </a>
                            <a href="{{ route('admin.faqs.index') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Details -->
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-header animated-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>FAQ Information
                        </h5>
                        <div>
                            @if($faq->is_active)
                                <span class="badge bg-success fs-6">Active</span>
                            @else
                                <span class="badge bg-danger fs-6">Inactive</span>
                            @endif
                            <span class="badge bg-info fs-6 ms-2">Order: {{ $faq->sort_order }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-question me-2 text-primary"></i>Question
                            </label>
                            <div class="fs-5 fw-semibold">{{ $faq->question }}</div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="info-item">
                            <label class="fw-bold text-muted mb-2">
                                <i class="fas fa-comment me-2 text-success"></i>Answer
                            </label>
                            <div class="fs-6">{{ $faq->answer }}</div>
                        </div>
                    </div>

                    @if($faq->campaign_section)
                        <div class="mb-4">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-tag me-2 text-info"></i>Campaign Section
                                </label>
                                <div class="fs-6">
                                    <span class="badge bg-secondary fs-6">{{ $faq->campaign_section }}</span>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-sort-numeric-up me-2 text-warning"></i>Sort Order
                                </label>
                                <div class="fs-6">{{ $faq->sort_order }}</div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-plus-circle me-2 text-primary"></i>Created At
                                </label>
                                <div class="fs-6">{{ $faq->created_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="info-item">
                                <label class="fw-bold text-muted mb-2">
                                    <i class="fas fa-edit me-2 text-warning"></i>Last Updated
                                </label>
                                <div class="fs-6">{{ $faq->updated_at->format('F d, Y H:i') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.faqs.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                        <div>
                            <a href="{{ route('admin.faqs.edit', $faq->id) }}" class="btn btn-warning me-2">
                                <i class="fas fa-edit me-2"></i>Edit FAQ
                            </a>
                            <form action="{{ route('admin.faqs.destroy', $faq->id) }}" 
                                  method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger" 
                                        onclick="return confirm('Are you sure you want to delete this FAQ?')">
                                    <i class="fas fa-trash me-2"></i>Delete FAQ
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('sweetalert::alert')

<style>
.info-item {
    padding: 1rem;
    background: linear-gradient(135deg, rgba(221, 160, 221, 0.1) 0%, rgba(186, 85, 211, 0.1) 100%);
    border-radius: 10px;
    border-left: 4px solid var(--bubblegum-purple);
    transition: all 0.3s ease;
}

.info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
@endsection
