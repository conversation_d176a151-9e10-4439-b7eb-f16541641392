<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add product_url to merchandise_packages table
        Schema::table('merchandise_packages', function (Blueprint $table) {
            $table->string('product_url', 255)->nullable()->after('description');
        });

        // Add product_url to special_rewards table
        Schema::table('special_rewards', function (Blueprint $table) {
            $table->string('product_url', 255)->nullable()->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove product_url from merchandise_packages table
        Schema::table('merchandise_packages', function (Blueprint $table) {
            $table->dropColumn('product_url');
        });

        // Remove product_url from special_rewards table
        Schema::table('special_rewards', function (Blueprint $table) {
            $table->dropColumn('product_url');
        });
    }
};
