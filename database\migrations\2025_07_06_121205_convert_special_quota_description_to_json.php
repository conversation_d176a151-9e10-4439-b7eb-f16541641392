<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, let's backup existing data and convert it to JSON format
        $specialQuotas = DB::table('special_quotas')->get();

        // Create a temporary column to store JSON data
        Schema::table('special_quotas', function (Blueprint $table) {
            $table->json('description_json')->nullable()->after('description');
        });

        // Convert existing HTML descriptions to JSON format
        foreach ($specialQuotas as $quota) {
            $jsonData = [
                'content' => $quota->description ?? '',
                'details' => [
                    // Add some default key-value pairs as examples
                    ['label' => 'Kuota Utuh', 'value' => $quota->quota_volume . ' GB'],
                    ['label' => 'Masa Aktif', 'value' => $quota->validity_days . ' hari'],
                    ['label' => 'Harga Normal', 'value' => 'Rp ' . number_format($quota->normal_price, 0, ',', '.')],
                    ['label' => 'Harga Spesial', 'value' => 'Rp ' . number_format($quota->special_price, 0, ',', '.')]
                ]
            ];

            DB::table('special_quotas')
                ->where('id', $quota->id)
                ->update(['description_json' => json_encode($jsonData)]);
        }

        // Drop the old description column and rename the new one
        Schema::table('special_quotas', function (Blueprint $table) {
            $table->dropColumn('description');
        });

        Schema::table('special_quotas', function (Blueprint $table) {
            $table->renameColumn('description_json', 'description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get current JSON data
        $specialQuotas = DB::table('special_quotas')->get();

        // Create temporary TEXT column
        Schema::table('special_quotas', function (Blueprint $table) {
            $table->text('description_text')->nullable()->after('description');
        });

        // Convert JSON back to HTML text
        foreach ($specialQuotas as $quota) {
            $jsonData = json_decode($quota->description, true);
            $htmlContent = $jsonData['content'] ?? '';

            DB::table('special_quotas')
                ->where('id', $quota->id)
                ->update(['description_text' => $htmlContent]);
        }

        // Drop JSON column and rename text column back
        Schema::table('special_quotas', function (Blueprint $table) {
            $table->dropColumn('description');
        });

        Schema::table('special_quotas', function (Blueprint $table) {
            $table->renameColumn('description_text', 'description');
        });
    }
};
