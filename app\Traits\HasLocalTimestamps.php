<?php

namespace App\Traits;

trait HasLocalTimestamps
{
    /**
     * Get the created_at timestamp in local timezone
     */
    public function getLocalCreatedAtAttribute()
    {
        return $this->created_at ? $this->created_at->setTimezone(config('app.timezone')) : null;
    }

    /**
     * Get the updated_at timestamp in local timezone
     */
    public function getLocalUpdatedAtAttribute()
    {
        return $this->updated_at ? $this->updated_at->setTimezone(config('app.timezone')) : null;
    }

    /**
     * Get the deleted_at timestamp in local timezone (for soft deletes)
     */
    public function getLocalDeletedAtAttribute()
    {
        return $this->deleted_at ? $this->deleted_at->setTimezone(config('app.timezone')) : null;
    }

    /**
     * Format created_at in local timezone
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->local_created_at ? $this->local_created_at->format('M d, Y H:i') : null;
    }

    /**
     * Format updated_at in local timezone
     */
    public function getFormattedUpdatedAtAttribute()
    {
        return $this->local_updated_at ? $this->local_updated_at->format('M d, Y H:i') : null;
    }

    /**
     * Format deleted_at in local timezone
     */
    public function getFormattedDeletedAtAttribute()
    {
        return $this->local_deleted_at ? $this->local_deleted_at->format('M d, Y H:i') : null;
    }
}
